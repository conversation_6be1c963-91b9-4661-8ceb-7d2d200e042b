{"name": "buyer_backend", "version": "1.0.0", "description": "**Edit a file, create a new file, and clone from Bitbucket in under 2 minutes**", "private": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "nx build", "build:all": "nx run-many --target=build --all", "build:api-gateway": "nx build buyer-api-gateway", "build:auth": "nx build buyer-auth-service", "build:profile": "nx build buyer-profile-service", "build:search": "nx build buyer-search-service", "build:product": "nx build buyer-product-service", "build:cart": "nx build buyer-cart-service", "build:order": "nx build buyer-order-service", "start": "nx run-many --target=serve --all", "start:custom": "nx run-many --target=serve --projects=buyer-auth-service,buyer-product-service", "start:auth": "nx serve buyer-auth-service", "start:profile": "nx serve buyer-profile-service", "start:product": "nx serve buyer-product-service ", "start:cart": "nx serve buyer-cart-service", "start:order": "nx serve buyer-order-service", "dev:all": "nx run-many --target=serve --all --watch", "dev:auth": "nx serve buyer-auth-service --watch", "dev:profile": "nx serve buyer-profile-service --watch", "dev:product": "nx serve buyer-product-service --watch", "dev:cart": "nx serve buyer-cart-service --watch", "dev:order": "nx serve buyer-order-service --watch", "lint": "nx lint", "lint:all": "nx run-many --target=lint --all", "test:all": "nx run-many --target=test --all", "sync:products": "nx run buyer-product-service:sync-products", "init:elasticsearch": "ts-node apps/buyer-product-service/src/scripts/init-elasticsearch.ts", "migrate:crops-to-products": "ts-node apps/buyer-product-service/src/scripts/migrate-crops-to-products.ts", "seed:crops": "ts-node apps/buyer-product-service/src/scripts/seed-crops.ts", "seed:farmers-farms": "ts-node apps/buyer-product-service/src/scripts/seed-farmers-farms.ts"}, "author": "", "license": "ISC", "dependencies": {"@elastic/elasticsearch": "^9.0.2", "@types/bcryptjs": "^3.0.0", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.97", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^7.1.0", "http-proxy-middleware": "^3.0.5", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "morgan": "^1.10.0", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.8.0", "@nx/eslint": "20.8.1", "@nx/eslint-plugin": "20.8.1", "@nx/express": "20.8.1", "@nx/jest": "20.8.1", "@nx/js": "20.8.1", "@nx/node": "20.8.1", "@nx/vite": "20.8.1", "@nx/web": "20.8.1", "@nx/webpack": "20.8.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@svgr/webpack": "^8.0.1", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/morgan": "^1.9.9", "@types/node": "~18.16.9", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.0", "concurrently": "^8.2.2", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "jsonc-eslint-parser": "^2.1.0", "prettier": "^2.6.2", "react-refresh": "^0.10.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "vite": "^6.0.0", "vite-plugin-dts": "~4.5.0", "vitest": "^3.0.0", "webpack-cli": "^5.1.4"}}