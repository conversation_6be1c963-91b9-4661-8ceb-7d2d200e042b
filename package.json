{"name": "buyer_frontend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nx serve apps/core", "dev": "nx serve apps/core", "build": "nx build apps/core", "preview": "nx preview apps/core"}, "repository": {"type": "git", "url": "git@bitbucket-in:haneef_shaik/buyer_frontend.git"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@nx/js": "21.0.3", "@nx/react": "^21.0.3", "@nx/vite": "^21.0.3", "@nx/web": "21.0.3", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/node": "^20.0.0", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/ui": "^3.0.0", "autoprefixer": "10.4.13", "jiti": "2.4.2", "jsdom": "~22.1.0", "nx": "21.0.3", "postcss": "8.4.38", "prettier": "^2.6.2", "tailwindcss": "3.4.3", "tslib": "^2.3.0", "typescript": "~5.7.2", "vite": "^6.0.0", "vite-plugin-dts": "~4.5.0", "vitest": "^3.0.0"}, "nx": {}, "dependencies": {"@reduxjs/toolkit": "^2.8.1", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.509.0", "react": "19.0.0", "react-dom": "19.0.0", "react-redux": "^9.2.0", "react-router-dom": "6.29.0", "react-toastify": "^11.0.5", "redux": "^5.0.1", "tailwind-merge": "^3.2.0"}}