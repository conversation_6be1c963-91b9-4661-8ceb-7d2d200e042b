{"info": {"name": "AgriTech Buyer Backend APIs", "description": "Comprehensive API collection for AgriTech Buyer Backend microservices including Authentication, Products, Orders, Profiles, Cart, and Dropdown services.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "auth_base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "product_base_url", "value": "http://localhost:6002", "type": "string"}, {"key": "profile_base_url", "value": "http://localhost:6006", "type": "string"}, {"key": "order_base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "cart_base_url", "value": "http://localhost:3333", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "item": [{"name": "🔐 Authentication Service", "description": "User authentication and authorization endpoints", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/register", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "register"]}, "description": "Register a new user account"}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.data.accessToken);", "    pm.collectionVariables.set('refresh_token', response.data.refreshToken);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/login", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "login"]}, "description": "Authenticate user and get access token"}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/refresh-token", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "refresh-token"]}, "description": "Refresh access token using refresh token"}}, {"name": "Validate Token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{auth_base_url}}/api/auth/validate", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "validate"]}, "description": "Validate current access token"}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/forgot-password", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "forgot-password"]}, "description": "Request password reset"}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset-token-here\",\n  \"newPassword\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/reset-password", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "reset-password"]}, "description": "Reset password using reset token"}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"SecurePassword123!\",\n  \"newPassword\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{auth_base_url}}/api/auth/change-password", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "change-password"]}, "description": "Change user password"}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{auth_base_url}}/api/auth/logout", "host": ["{{auth_base_url}}"], "path": ["api", "auth", "logout"]}, "description": "Logout user and invalidate token"}}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{auth_base_url}}/health", "host": ["{{auth_base_url}}"], "path": ["health"]}, "description": "Check auth service health"}}]}, {"name": "🌾 Product Service", "description": "Product management, search, categories, and dropdown endpoints", "item": [{"name": "📦 Products", "item": [{"name": "Get Products", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/products?page=1&limit=20&categoryId=&sellerId=&minPrice=&maxPrice=&searchTerm=", "host": ["{{product_base_url}}"], "path": ["api", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "categoryId", "value": ""}, {"key": "sellerId", "value": ""}, {"key": "minPrice", "value": ""}, {"key": "maxPrice", "value": ""}, {"key": "searchTerm", "value": ""}]}, "description": "Get list of products with filtering and pagination"}}, {"name": "Get Product by ID", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/products/:productId", "host": ["{{product_base_url}}"], "path": ["api", "products", ":productId"], "variable": [{"key": "productId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get single product by ID"}}]}, {"name": "🔍 Search", "item": [{"name": "Search Products", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/search?query=tomato&page=1&limit=20", "host": ["{{product_base_url}}"], "path": ["api", "search"], "query": [{"key": "query", "value": "tomato"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}, "description": "Search products using Elasticsearch"}}, {"name": "Elasticsearch Health", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/api/search/health", "host": ["{{product_base_url}}"], "path": ["api", "search", "health"]}, "description": "Check Elasticsearch health status"}}]}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{product_base_url}}/health", "host": ["{{product_base_url}}"], "path": ["health"]}, "description": "Check product service health"}}]}, {"name": "👤 Profile Service", "description": "User profile and address management endpoints", "item": [{"name": "👤 Profile Management", "item": [{"name": "Get My Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/profile", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile"]}, "description": "Get current user's profile"}}, {"name": "Get Profile by ID", "request": {"method": "GET", "url": {"raw": "{{profile_base_url}}/api/profiles/profile/:profileId", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile", ":profileId"], "variable": [{"key": "profileId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get profile by ID (public access)"}}, {"name": "Create Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"displayName\": \"John D\",\n  \"dateOfBirth\": \"1990-01-15\",\n  \"gender\": \"male\",\n  \"phoneNumber\": \"+91-9876543210\",\n  \"bio\": \"Passionate about sustainable agriculture\",\n  \"interests\": [\"organic farming\", \"crop rotation\", \"sustainable practices\"]\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/profile", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile"]}, "description": "Create new user profile"}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"displayName\": \"<PERSON> Do<PERSON>\",\n  \"bio\": \"Updated bio - Passionate about sustainable agriculture and technology\",\n  \"interests\": [\"organic farming\", \"agtech\", \"sustainable practices\"]\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/profile", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile"]}, "description": "Update user profile"}}, {"name": "Delete Profile", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/profile", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile"]}, "description": "Delete user profile"}}, {"name": "Search Profiles", "request": {"method": "GET", "url": {"raw": "{{profile_base_url}}/api/profiles/profiles/search?query=john&page=1&limit=10", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profiles", "search"], "query": [{"key": "query", "value": "john"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Search profiles"}}, {"name": "Update Profile Picture", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"profilePicture\": \"https://example.com/profile-pictures/john-doe.jpg\"\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/profile/picture", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile", "picture"]}, "description": "Update profile picture"}}, {"name": "Verify Profile (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/profile/:userId/verify", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "profile", ":userId", "verify"], "variable": [{"key": "userId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Verify profile (admin/moderator only)"}}]}, {"name": "🏠 Address Management", "item": [{"name": "Get All Addresses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses"]}, "description": "Get all user addresses"}}, {"name": "Get Address by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/:addressId", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", ":addressId"], "variable": [{"key": "addressId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get specific address by ID"}}, {"name": "Get Default Address", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/default", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", "default"]}, "description": "Get default address"}}, {"name": "Add Address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"street\": \"123 Farm Road\",\n  \"city\": \"Bangalore\",\n  \"state\": \"Karnataka\",\n  \"postalCode\": \"560001\",\n  \"country\": \"India\",\n  \"label\": \"Home\",\n  \"isDefault\": true,\n  \"coordinates\": {\n    \"latitude\": 12.9716,\n    \"longitude\": 77.5946\n  }\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/addresses", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses"]}, "description": "Add new address"}}, {"name": "Update Address", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"street\": \"456 Updated Farm Road\",\n  \"city\": \"Bangalore\",\n  \"state\": \"Karnataka\",\n  \"postalCode\": \"560002\",\n  \"country\": \"India\",\n  \"label\": \"Home - Updated\"\n}"}, "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/:addressId", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", ":addressId"], "variable": [{"key": "addressId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Update existing address"}}, {"name": "Delete Address", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/:addressId", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", ":addressId"], "variable": [{"key": "addressId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Delete address"}}, {"name": "<PERSON> Default Address", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{profile_base_url}}/api/profiles/addresses/:addressId/default", "host": ["{{profile_base_url}}"], "path": ["api", "profiles", "addresses", ":addressId", "default"], "variable": [{"key": "addressId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Set address as default"}}]}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{profile_base_url}}/health", "host": ["{{profile_base_url}}"], "path": ["health"]}, "description": "Check profile service health"}}]}, {"name": "📦 Order Service", "description": "Order management and processing endpoints", "item": [{"name": "Service Info", "request": {"method": "GET", "url": {"raw": "{{order_base_url}}/api", "host": ["{{order_base_url}}"], "path": ["api"]}, "description": "Get order service information and available endpoints"}}, {"name": "Get All Orders", "request": {"method": "GET", "url": {"raw": "{{order_base_url}}/api/orders?page=1&limit=10", "host": ["{{order_base_url}}"], "path": ["api", "orders"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get all orders with pagination"}}, {"name": "Get Order by ID", "request": {"method": "GET", "url": {"raw": "{{order_base_url}}/api/orders/:orderId", "host": ["{{order_base_url}}"], "path": ["api", "orders", ":orderId"], "variable": [{"key": "orderId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get order details by ID"}}, {"name": "Get Orders by User ID", "request": {"method": "GET", "url": {"raw": "{{order_base_url}}/api/users/:userId/orders", "host": ["{{order_base_url}}"], "path": ["api", "users", ":userId", "orders"], "variable": [{"key": "userId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Get orders for a specific user"}}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"507f1f77bcf86cd799439011\",\n  \"items\": [\n    {\n      \"productId\": \"507f1f77bcf86cd799439012\",\n      \"sellerId\": \"507f1f77bcf86cd799439013\",\n      \"productType\": \"CROP\",\n      \"productSnapshot\": {\n        \"name\": \"Organic Tomatoes\",\n        \"description\": \"Fresh organic tomatoes from Karnataka\",\n        \"category\": \"Vegetables\",\n        \"images\": [\"https://example.com/tomato1.jpg\"]\n      },\n      \"quantity\": 10,\n      \"unit\": \"kg\",\n      \"unitPrice\": 50.00,\n      \"totalPrice\": 500.00,\n      \"qualityRequirements\": {\n        \"grade\": \"A\",\n        \"specifications\": [\"Organic certified\", \"Fresh harvest\"]\n      }\n    }\n  ],\n  \"totalAmount\": 500.00,\n  \"currency\": \"INR\",\n  \"paymentMethod\": \"UPI\",\n  \"deliveryMethod\": \"PICKUP\",\n  \"notes\": \"Please ensure fresh quality\"\n}"}, "url": {"raw": "{{order_base_url}}/api/orders", "host": ["{{order_base_url}}"], "path": ["api", "orders"]}, "description": "Create a new order with detailed agricultural product information"}}, {"name": "Update Order Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"CONFIRMED\",\n  \"notes\": \"Order confirmed by seller\"\n}"}, "url": {"raw": "{{order_base_url}}/api/orders/:orderId/status", "host": ["{{order_base_url}}"], "path": ["api", "orders", ":orderId", "status"], "variable": [{"key": "orderId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Update order status"}}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Changed my mind\",\n  \"notes\": \"Customer requested cancellation\"\n}"}, "url": {"raw": "{{order_base_url}}/api/orders/:orderId/cancel", "host": ["{{order_base_url}}"], "path": ["api", "orders", ":orderId", "cancel"], "variable": [{"key": "orderId", "value": "507f1f77bcf86cd799439011"}]}, "description": "Cancel an order"}}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{order_base_url}}/health", "host": ["{{order_base_url}}"], "path": ["health"]}, "description": "Check order service health"}}]}, {"name": "🛒 Cart Service", "description": "Shopping cart management endpoints", "item": [{"name": "Welcome Message", "request": {"method": "GET", "url": {"raw": "{{cart_base_url}}/api", "host": ["{{cart_base_url}}"], "path": ["api"]}, "description": "Get cart service welcome message"}}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{cart_base_url}}/health", "host": ["{{cart_base_url}}"], "path": ["health"]}, "description": "Check cart service health"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Pre-request script for all requests", "console.log('Making request to:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has success field', function () {", "    if (pm.response.code === 200) {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('success');", "    }", "});"]}}]}