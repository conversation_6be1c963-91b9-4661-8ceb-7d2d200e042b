# AgriTech Buyer Backend - Postman Collection

## 📋 Overview

This folder contains a comprehensive Postman collection for testing all APIs in the AgriTech Buyer Backend microservices architecture. The collection includes endpoints from:

- **🔐 Authentication Service** (Port 3000) - User authentication and authorization
- **🌾 Product Service** (Port 6002) - Product management and search functionality
- **👤 Profile Service** (Port 3334) - User profiles and address management
- **📦 Order Service** (Port 3001) - Order management and processing
- **🛒 Cart Service** (Port 3333) - Shopping cart functionality

## 📁 Files

- `AgriTech_Buyer_Backend_APIs.postman_collection.json` - Main collection file
- `readme.md` - This documentation file

## 🚀 Quick Start

### 1. Import the Collection

1. Open Postman
2. Click **Import** button
3. Select the `AgriTech_Buyer_Backend_APIs.postman_collection.json` file
4. Click **Import**

### 2. Environment Variables

The collection uses these pre-configured variables:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `auth_base_url` | `http://localhost:3000` | Authentication service URL |
| `product_base_url` | `http://localhost:6002` | Product service URL |
| `profile_base_url` | `http://localhost:3334` | Profile service URL |
| `order_base_url` | `http://localhost:3001` | Order service URL |
| `cart_base_url` | `http://localhost:3333` | Cart service URL |
| `auth_token` | (auto-populated) | JWT access token |
| `refresh_token` | (auto-populated) | JWT refresh token |

### 3. Authentication Workflow

1. **Register** a new user (if needed)
2. **Login** to get authentication tokens (automatically saved)
3. Use authenticated endpoints with Bearer token

## 📊 Collection Structure

### 🔐 Authentication Service (9 endpoints)
- Register User, Login User, Refresh Token
- Validate Token, Forgot Password, Reset Password
- Change Password, Logout, Health Check

### 🌾 Product Service (5 endpoints)

#### 📦 Products (2 endpoints)
- Get Products (with filters), Get Product by ID

#### 🔍 Search (2 endpoints)
- Search products with Elasticsearch
- Elasticsearch health check

### 👤 Profile Service (15 endpoints)

#### 👤 Profile Management (8 endpoints)
- CRUD operations for user profiles
- Search profiles, Update picture, Verify profile

#### 🏠 Address Management (7 endpoints)
- CRUD operations for user addresses
- Default address management

### 📦 Order Service (8 endpoints)
- Service Info, Get All Orders, Get Order by ID
- Get Orders by User ID, Create Order (with agricultural details)
- Update Order Status, Cancel Order, Health Check

### 🛒 Cart Service (2 endpoints)
- Welcome Message, Health Check

## 🔧 Usage Examples

### Authentication Flow
```javascript
// 1. Register (if needed)
POST {{auth_base_url}}/api/auth/register

// 2. Login (tokens auto-saved)
POST {{auth_base_url}}/api/auth/login

// 3. Use authenticated endpoints
GET {{product_base_url}}/api/products
Authorization: Bearer {{auth_token}}
```

### Product Search Flow
```javascript
// 1. Search products
GET {{product_base_url}}/api/search?query=tomato&page=1&limit=20

// 2. Get product details
GET {{product_base_url}}/api/products/{{productId}}

// 3. Check Elasticsearch health
GET {{product_base_url}}/api/search/health
```

### Profile & Address Flow
```javascript
// 1. Create profile
POST {{profile_base_url}}/api/profiles/profile

// 2. Add address
POST {{profile_base_url}}/api/profiles/addresses

// 3. Set as default
PUT {{profile_base_url}}/api/profiles/addresses/{{addressId}}/default
```

## 🧪 Testing Features

### Automatic Token Management
- Login automatically saves `auth_token` and `refresh_token`
- All authenticated requests use Bearer token
- Token validation and refresh capabilities

### Global Test Scripts
- Response time validation (< 5000ms)
- Success field validation for 200 responses
- Automatic request URL logging

### Pre-request Scripts
- Request URL logging for debugging
- Environment variable validation

## 🛠️ Customization

### Changing Service URLs
Update collection variables if services run on different ports:

```json
{
  "auth_base_url": "http://your-auth-service:3000",
  "product_base_url": "http://your-product-service:6002",
  "order_base_url": "http://your-order-service:3001"
}
```

### Adding Custom Headers
Add headers at collection or request level:

```json
{
  "key": "X-API-Version",
  "value": "v1",
  "type": "text"
}
```

## 🔍 Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Login first to get valid token
   - Check if token expired (use refresh)

2. **404 Not Found**
   - Verify service is running on correct port
   - Check base URL configuration

3. **500 Internal Server Error**
   - Check service logs and database connections

### Debug Tips
- Use Postman Console for request/response logs
- Verify collection variables are set
- Test service health endpoints first

## 📈 API Coverage

- **Total Requests**: 25+ endpoints
- **Services Covered**: 5 microservices
- **Authentication**: JWT Bearer token
- **Test Coverage**: Response validation
- **Documentation**: Comprehensive descriptions

## 🚀 Current Implementation Status

- ✅ **Order Service**: Fully functional with simplified architecture (Port 3001)
- ✅ **Product Service**: Basic product listing and search (Port 6002)
- ⚠️ **Authentication Service**: Verify current port configuration (Port 3000)
- ⚠️ **Profile Service**: Verify current implementation (Port 3334)
- ⚠️ **Cart Service**: Basic structure in place (Port 3333)

## 🌾 Agricultural Features

### Order Service Highlights
- **Detailed Product Information**: Product snapshots with agricultural details
- **Seller Integration**: References to farmer/seller data from shared database
- **Quality Requirements**: Agricultural-specific quality specifications
- **Flexible Delivery**: Supports pickup and delivery methods for farm products

### Database Architecture
- **Shared MongoDB**: Both buyer and seller apps use the same database
- **Elasticsearch**: Centralized search at ***********:9200
- **Event-Driven**: Kafka-based order processing and payment events

## 🔗 Related Documentation

- [Service Architecture](../README.md)
- [Individual Service Documentation](../apps/)

---

**Ready to test your AgriTech APIs! 🚀**