import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for Farm document
export interface FarmDocument extends Document {
  farmId: string;
  name: string;
  location: {
    country: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  certifications: string[];
  farmingPractices: {
    primaryMethod: string;
    irrigationSystems: string[];
    sustainabilityScore?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

const FarmSchema: Schema = new Schema<FarmDocument>({
  farmId: { type: String, required: true, unique: true, index: true },
  name: { type: String, required: true },
  location: {
    country: { type: String, required: true },
    state: { type: String, required: true },
    city: { type: String, required: true },
    pincode: { type: String, required: true },
    addressLine1: { type: String, required: true },
    addressLine2: { type: String },
    coordinates: {
      latitude: { type: Number },
      longitude: { type: Number }
    }
  },
  totalArea: { type: Number, required: true, min: 0 },
  soilType: { type: String, required: true },
  waterSource: { type: String, required: true },
  certifications: [{ type: String }],
  farmingPractices: {
    primaryMethod: { type: String, required: true },
    irrigationSystems: [{ type: String }],
    sustainabilityScore: { type: Number, min: 0, max: 100 }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

export const FarmModel = mongoose.model<FarmDocument>('Farm', FarmSchema);
