import mongoose, { Schema, Document, Model, Query, Types } from 'mongoose';

// Define options for find queries to include deleted documents
interface FindWithDeletedOptions {
  withDeleted?: boolean;
}

// Extend Mongoose Query type to include custom options
interface ProductQuery<T extends Document> extends Query<T | null, T, Record<string, unknown>, T> {
  getOptions(): FindWithDeletedOptions;
}

/**
 * Product types enum
 */
export enum ProductType {
  PLOT = 'PLOT',
  CROP = 'CROP',
  FARM_SERVICE = 'FARM_SERVICE',
  EQUIPMENT = 'EQUIPMENT',
  SUPPLY = 'SUPPLY',
}

/**
 * Crop quality grade enum
 */
export enum CropQualityGrade {
  PREMIUM = 'PREMIUM',
  STANDARD = 'STANDARD',
  ECONOMY = 'ECONOMY',
}

/**
 * Crop growth stage enum
 */
export enum CropGrowthStage {
  PLANTING = 'PLANTING',
  GROWING = 'GROWING',
  MATURING = 'MATURING',
  READY = 'READY',
}

/**
 * Crop health status enum
 */
export enum CropHealthStatus {
  HEALTHY = 'HEALTHY',
  WARNING = 'WARNING',
  CRITICAL = 'CRITICAL',
}

/**
 * Location interface
 */
export interface Location {
  country: string;
  state: string;
  city: string;
  pincode: string;
  addressLine1: string;
  addressLine2?: string;
  latitude?: number;
  longitude?: number;
}

/**
 * Price interface
 */
export interface Price {
  amount: number;
  currency: string;
  unit?: string; // per kg, per acre, etc.
}

/**
 * Availability interface
 */
export interface Availability {
  status: 'AVAILABLE' | 'OUT_OF_STOCK' | 'LIMITED';
  quantity?: number;
}

/**
 * Harvest date range interface
 */
export interface HarvestDateRange {
  startDate?: Date;
  endDate?: Date;
}

/**
 * Nutritional info interface
 */
export interface NutritionalInfo {
  calories?: number;
  protein?: number;
  carbs?: number;
  fiber?: number;
  vitamins?: string[];
}

/**
 * Cultivation info interface
 */
export interface CultivationInfo {
  irrigationNeeds?: string;
  fertilizerRequirements?: string;
  pestControl?: string;
  climateConditions?: string;
}

/**
 * Post harvest info interface
 */
export interface PostHarvestInfo {
  storageRequirements?: string;
  shelfLife?: string;
  processingMethods?: string[];
}

/**
 * Sustainability info interface
 */
export interface SustainabilityInfo {
  waterUsage?: string;
  carbonFootprint?: string;
  pesticide?: string;
}

/**
 * Product attributes interface
 */
export interface ProductAttributes {
  // Common attributes
  images?: string[];
  tags?: string[];
  rating?: number;

  // Plot-specific attributes
  size?: number;
  location?: Location;
  soilType?: string;
  waterAvailability?: string;

  // Crop-specific attributes
  cropId?: string;
  cropCategory?: string;
  variety?: string;
  farmingMethod?: string;
  growthStage?: CropGrowthStage;
  irrigationMethod?: string;
  harvestSeason?: string;
  ownershipUnit?: string;
  pestDiseaseStatus?: string;
  storageMethod?: string;
  nutrientManagement?: string;
  waterSource?: string;
  pesticideUsage?: string;
  seedType?: string;
  cropHealthStatus?: CropHealthStatus;
  harvestingMethod?: string;
  packagingType?: string;
  transportationOptions?: string[];
  estimatedDeliveryTime?: string;
  deliveryCharges?: number;
  fulfillmentStatus?: string;
  riskFactors?: string[];

  plantingDate?: Date;
  expectedHarvestDate?: Date;
  actualHarvestDate?: Date;
  
  health?: {
    status: CropHealthStatus;
    issues: string[];
    lastCheck: Date;
  };

  yield?: {
    expected: number;
    actual: number;
    unit: string;
  };

  resources?: {
    water: number;
    fertilizer: number;
    pesticides: number;
  };

  soilConditions?: {
    type: string;
    ph: number;
    nutrients: string[];
    organicMatter?: number;
    drainage?: string;
    salinity?: string;
  };

  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fiber?: number;
    vitamins?: string[];
    minerals?: string[];
    antioxidants?: string[];
  };

  cultivation?: {
    irrigationNeeds: string;
    fertilizerRequirements: string;
    pestControl: string;
    climateConditions: string;
    sowingMethod?: string;
    spacingRequirements?: string;
    companionCrops?: string[];
  };

  postHarvest?: {
    storageRequirements: string;
    shelfLife: string;
    processingMethods: string[];
    valueAddedProducts?: string[];
    marketDemand?: string;
    exportPotential?: boolean;
  };

  sustainability?: {
    waterUsage: string;
    carbonFootprint: string;
    pesticide: string;
    biodiversityImpact?: string;
    soilHealth?: string;
    renewableEnergy?: boolean;
  };

  maintenance?: {
    schedule: {
      irrigation: Date[];
      fertilization: Date[];
      pestControl: Date[];
      inspection: Date[];
    };
    history: {
      activities: {
        type: string;
        date: Date;
        description: string;
        performedBy: string;
      }[];
    };
  };

  weather?: {
    forecasts: {
      date: Date;
      temperature: {
        min: number;
        max: number;
        unit: string;
      };
      precipitation: {
        amount: number;
        unit: string;
        type: string;
      };
      humidity: number;
      windSpeed: {
        value: number;
        unit: string;
      };
    }[];
    alerts: {
      type: string;
      severity: string;
      description: string;
      startDate: Date;
      endDate: Date;
    }[];
  };

  // Market-specific attributes
  market?: {
    demandLevel?: 'HIGH' | 'MEDIUM' | 'LOW';
    priceVolatility?: 'HIGH' | 'MEDIUM' | 'LOW';
    seasonalDemand?: string[];
    targetMarkets?: string[];
    competitionLevel?: 'HIGH' | 'MEDIUM' | 'LOW';
  };

  // Farmer-related attributes
  farmerExperienceLevel?: string;
  farmerCertification?: string;
  farmerReputationScore?: number;

  // Transaction-related attributes
  ownershipStatus?: string;
  paymentMethods?: string[];
  pricePerOwnershipUnit?: number;
  paymentTerms?: string;
  cancellationPolicy?: string;
  agreementDocumentUrl?: string;
  taxInformation?: string;

  // Platform usage
  listingCreationDate?: Date;
  listingExpiryDate?: Date;
  viewCount?: number;
  wishlistCount?: number;

  farmId?: string;
  plotId?: string;

  // Farm service-specific attributes
  serviceArea?: string;
  serviceDuration?: string;
  equipmentUsed?: string[];

  // Equipment/Supply specific attributes
  brand?: string;
  model?: string;
  condition?: string;
  expiryDate?: Date;
}

/**
 * Comprehensive Product document interface
 */
export interface ProductDocument extends Document {
  id: string;
  type: ProductType;
  sellerId: mongoose.Types.ObjectId;
  name: string;
  description: string;
  category: string;
  subCategory?: string;
  price: Price;
  availability: Availability;
  attributes: ProductAttributes;
  certifications?: string[];
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null; // For soft delete
  isActive: boolean; // To manage product availability

  // Virtual properties
  isAvailable: boolean;
}

// Location schema
const LocationSchema = new Schema({
  country: { type: String, required: true, default: 'INDIA' },
  state: { type: String, required: true },
  city: { type: String, required: true },
  pincode: { type: String, required: true },
  addressLine1: { type: String, required: true },
  addressLine2: { type: String },
  latitude: { type: Number },
  longitude: { type: Number }
}, { _id: false });

// Price schema
const PriceSchema = new Schema({
  amount: { type: Number, required: true, min: 0 },
  currency: { type: String, required: true, default: 'INR' },
  unit: { type: String }
}, { _id: false });

// Availability schema
const AvailabilitySchema = new Schema({
  status: {
    type: String,
    required: true,
    enum: ['AVAILABLE', 'OUT_OF_STOCK', 'LIMITED'],
    default: 'AVAILABLE'
  },
  quantity: { type: Number, min: 0 }
}, { _id: false });

// Harvest date range schema
const HarvestDateRangeSchema = new Schema({
  startDate: { type: Date },
  endDate: { type: Date }
}, { _id: false });

// Nutritional info schema
const NutritionalInfoSchema = new Schema({
  calories: { type: Number },
  protein: { type: Number },
  carbs: { type: Number },
  fiber: { type: Number },
  vitamins: [{ type: String }]
}, { _id: false });

// Cultivation info schema
const CultivationInfoSchema = new Schema({
  irrigationNeeds: { type: String },
  fertilizerRequirements: { type: String },
  pestControl: { type: String },
  climateConditions: { type: String }
}, { _id: false });

// Post harvest info schema
const PostHarvestInfoSchema = new Schema({
  storageRequirements: { type: String },
  shelfLife: { type: String },
  processingMethods: [{ type: String }]
}, { _id: false });

// Sustainability info schema
const SustainabilityInfoSchema = new Schema({
  waterUsage: { type: String },
  carbonFootprint: { type: String },
  pesticide: { type: String }
}, { _id: false });

// Product attributes schema
const ProductAttributesSchema = new Schema({
  // Common attributes
  images: [{ type: String }],
  tags: [{ type: String }],
  rating: { type: Number, min: 0, max: 5 },

  // Plot-specific attributes
  size: { type: Number },
  location: LocationSchema,
  soilType: { type: String },
  waterAvailability: { type: String },

  // Crop-specific attributes
  cropId: { type: String },
  cropCategory: { type: String },
  variety: { type: String },
  farmingMethod: { type: String },
  growthStage: { 
    type: String,
    enum: Object.values(CropGrowthStage)
  },
  irrigationMethod: { type: String },
  harvestSeason: { type: String },
  ownershipUnit: { type: String },
  pestDiseaseStatus: { type: String },
  storageMethod: { type: String },
  nutrientManagement: { type: String },
  waterSource: { type: String },
  pesticideUsage: { type: String },
  seedType: { type: String },
  cropHealthStatus: {
    type: String,
    enum: Object.values(CropHealthStatus)
  },
  harvestingMethod: { type: String },
  packagingType: { type: String },
  transportationOptions: [{ type: String }],
  estimatedDeliveryTime: { type: String },
  deliveryCharges: { type: Number },
  fulfillmentStatus: { type: String },
  riskFactors: [{ type: String }],

  plantingDate: { type: Date },
  expectedHarvestDate: { type: Date },
  actualHarvestDate: { type: Date },

  health: {
    status: { 
      type: String,
      enum: Object.values(CropHealthStatus)
    },
    issues: [{ type: String }],
    lastCheck: { type: Date }
  },

  yield: {
    expected: { type: Number },
    actual: { type: Number },
    unit: { type: String }
  },

  resources: {
    water: { type: Number },
    fertilizer: { type: Number },
    pesticides: { type: Number }
  },

  soilConditions: {
    type: { type: String },
    ph: { type: Number },
    nutrients: [{ type: String }],
    organicMatter: { type: Number },
    drainage: { type: String },
    salinity: { type: String }
  },

  nutritionalInfo: {
    calories: { type: Number },
    protein: { type: Number },
    carbs: { type: Number },
    fiber: { type: Number },
    vitamins: [{ type: String }],
    minerals: [{ type: String }],
    antioxidants: [{ type: String }]
  },

  cultivation: {
    irrigationNeeds: { type: String },
    fertilizerRequirements: { type: String },
    pestControl: { type: String },
    climateConditions: { type: String },
    sowingMethod: { type: String },
    spacingRequirements: { type: String },
    companionCrops: [{ type: String }]
  },

  postHarvest: {
    storageRequirements: { type: String },
    shelfLife: { type: String },
    processingMethods: [{ type: String }],
    valueAddedProducts: [{ type: String }],
    marketDemand: { type: String },
    exportPotential: { type: Boolean }
  },

  sustainability: {
    waterUsage: { type: String },
    carbonFootprint: { type: String },
    pesticide: { type: String },
    biodiversityImpact: { type: String },
    soilHealth: { type: String },
    renewableEnergy: { type: Boolean }
  },

  maintenance: {
    schedule: {
      irrigation: [{ type: Date }],
      fertilization: [{ type: Date }],
      pestControl: [{ type: Date }],
      inspection: [{ type: Date }]
    },
    history: {
      activities: [{
        type: { type: String },
        date: { type: Date },
        description: { type: String },
        performedBy: { type: String }
      }]
    }
  },

  weather: {
    forecasts: [{
      date: { type: Date },
      temperature: {
        min: { type: Number },
        max: { type: Number },
        unit: { type: String }
      },
      precipitation: {
        amount: { type: Number },
        unit: { type: String },
        type: { type: String }
      },
      humidity: { type: Number },
      windSpeed: {
        value: { type: Number },
        unit: { type: String }
      }
    }],
    alerts: [{
      type: { type: String },
      severity: { type: String },
      description: { type: String },
      startDate: { type: Date },
      endDate: { type: Date }
    }]
  },

  market: {
    demandLevel: { 
      type: String,
      enum: ['HIGH', 'MEDIUM', 'LOW']
    },
    priceVolatility: {
      type: String,
      enum: ['HIGH', 'MEDIUM', 'LOW']
    },
    seasonalDemand: [{ type: String }],
    targetMarkets: [{ type: String }],
    competitionLevel: {
      type: String,
      enum: ['HIGH', 'MEDIUM', 'LOW']
    }
  },

  // Farmer-related attributes
  farmerExperienceLevel: { type: String },
  farmerCertification: { type: String },
  farmerReputationScore: { type: Number, min: 0, max: 5 },

  // Transaction-related attributes
  ownershipStatus: { type: String },
  paymentMethods: [{ type: String }],
  pricePerOwnershipUnit: { type: Number },
  paymentTerms: { type: String },
  cancellationPolicy: { type: String },
  agreementDocumentUrl: { type: String },
  taxInformation: { type: String },

  // Platform usage
  listingCreationDate: { type: Date },
  listingExpiryDate: { type: Date },
  viewCount: { type: Number, default: 0 },
  wishlistCount: { type: Number, default: 0 },

  farmId: { type: String },
  plotId: { type: String },

  // Farm service-specific attributes
  serviceArea: { type: String },
  serviceDuration: { type: String },
  equipmentUsed: [{ type: String }],

  // Equipment/Supply specific attributes
  brand: { type: String },
  model: { type: String },
  condition: { type: String },
  expiryDate: { type: Date }
}, { _id: false });

const ProductSchema: Schema = new Schema<ProductDocument>({
  type: {
    type: String,
    required: true,
    enum: Object.values(ProductType),
    index: true
  },
  sellerId: { type: Schema.Types.ObjectId, required: true, index: true },
  name: { type: String, required: true, index: true },
  description: { type: String, required: true },
  category: { type: String, required: true, index: true },
  subCategory: { type: String, index: true },
  price: { type: PriceSchema, required: true },
  availability: { type: AvailabilitySchema, required: true },
  attributes: { type: ProductAttributesSchema, default: {} },
  certifications: [{ type: String }],
  deletedAt: { type: Date, default: null, index: true },
  isActive: { type: Boolean, default: true, index: true },
}, {
  timestamps: true, // Automatically manages createdAt and updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
ProductSchema.index({ name: 'text', description: 'text' });
ProductSchema.index({ 'attributes.location.state': 1, 'attributes.location.city': 1 });
ProductSchema.index({ 'price.amount': 1 });
ProductSchema.index({ 'attributes.cropCategory': 1 });
ProductSchema.index({ 'attributes.farmingMethod': 1 });
ProductSchema.index({ 'attributes.qualityGrade': 1 });
ProductSchema.index({ 'availability.status': 1 });
ProductSchema.index({ 'attributes.harvestSeason': 1 });

// Soft delete pre hook for find operations
ProductSchema.pre(/^find/, function(this: ProductQuery<ProductDocument>, next) {
  // 'this' refers to the query
  if (!this.getOptions().withDeleted) {
    this.where({ deletedAt: null });
  }
  next();
});

// Virtual for availability based on availability status and isActive
ProductSchema.virtual('isAvailable').get(function(this: ProductDocument) {
  return this.isActive &&
         this.availability.status === 'AVAILABLE' &&
         !this.deletedAt;
});

// Virtual for ID (for Elasticsearch compatibility)
ProductSchema.virtual('id').get(function(this: ProductDocument & { _id: Types.ObjectId }) {
  return this._id.toHexString();
});

// Ensure virtual fields are serialized
ProductSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    ret.id = ret._id;
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

export const ProductModel = mongoose.model<ProductDocument>('Product', ProductSchema, 'crops');