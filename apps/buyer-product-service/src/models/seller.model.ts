import mongoose, { Document, Schema } from 'mongoose';

export interface SellerDocument extends Document {
  _id: mongoose.Types.ObjectId;
  sellerId: string;
  personalInfo: {
    name: string;
    contact: string;
    email: string;
    address: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2?: string;
    };
  };
  documents: {
    identityProof: string;
    landOwnership: string;
    certifications: string[];
  };
  bankDetails: {
    accountNumber: string;
    bankName: string;
    ifscCode: string;
  };
  status: string;
  verificationStatus: string;
  farms: mongoose.Types.ObjectId[];
  password: string;
  createdAt: Date;
  updatedAt: Date;
  statusHistory: Array<{
    status: string;
    updatedBy: string;
    reason: string;
    updatedAt: Date;
    _id: mongoose.Types.ObjectId;
  }>;
  __v: number;
}

const SellerSchema = new Schema({
  sellerId: { type: String, required: true, unique: true },
  personalInfo: {
    name: { type: String, required: true },
    contact: { type: String, required: true },
    email: { type: String, required: true },
    address: {
      country: { type: String, required: true },
      state: { type: String, required: true },
      city: { type: String, required: true },
      pincode: { type: String, required: true },
      addressLine1: { type: String, required: true },
      addressLine2: { type: String }
    }
  },
  documents: {
    identityProof: { type: String, required: true },
    landOwnership: { type: String, required: true },
    certifications: [{ type: String }]
  },
  bankDetails: {
    accountNumber: { type: String, required: true },
    bankName: { type: String, required: true },
    ifscCode: { type: String, required: true }
  },
  status: { type: String, required: true },
  verificationStatus: { type: String, required: true },
  farms: [{ type: Schema.Types.ObjectId, ref: 'Farm' }],
  password: { type: String, required: true },
  statusHistory: [{
    status: { type: String, required: true },
    updatedBy: { type: String, required: true },
    reason: { type: String, required: true },
    updatedAt: { type: Date, required: true },
    _id: { type: Schema.Types.ObjectId }
  }]
}, {
  timestamps: true,
  collection: 'sellers'
});

export const SellerModel = mongoose.model<SellerDocument>('Seller', SellerSchema);
