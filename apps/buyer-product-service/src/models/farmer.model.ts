import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for Farmer document
export interface FarmerDocument extends Document {
  farmerId: string;
  name: string;
  contact: string;
  email: string;
  address: {
    country: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2?: string;
  };
  verificationStatus: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

const FarmerSchema: Schema = new Schema<FarmerDocument>({
  farmerId: { type: String, required: true, unique: true, index: true },
  name: { type: String, required: true },
  contact: { type: String, required: true },
  email: { type: String, required: true },
  address: {
    country: { type: String, required: true },
    state: { type: String, required: true },
    city: { type: String, required: true },
    pincode: { type: String, required: true },
    addressLine1: { type: String, required: true },
    addressLine2: { type: String }
  },
  verificationStatus: { type: String, required: true, default: 'pending' },
  status: { type: String, required: true, default: 'active' }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

export const FarmerModel = mongoose.model<FarmerDocument>('Farmer', FarmerSchema);
