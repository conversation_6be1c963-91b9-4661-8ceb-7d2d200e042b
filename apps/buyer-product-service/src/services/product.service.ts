import { config } from '../config';
import { logger } from '../utils/logger';
import { ProductModel, ProductDocument } from '../models/product.model';
import mongoose, { FilterQuery } from 'mongoose';
import SyncService from './sync.service';
import { FarmerModel, FarmerDocument } from '../models/farmer.model';
import { FarmModel, FarmDocument } from '../models/farm.model';
import { SellerModel, SellerDocument } from '../models/seller.model';

export interface SearchOptions {
  query?: string;
  page?: number;
  limit?: number;
  filters?: {
    minPrice?: number;
    maxPrice?: number;
    sellerId?: string;
    categories?: string[];
  };
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  }[];
}

export interface EnrichedProduct extends ProductDocument {
  farmer?: {
    farmerId: string;
    name: string;
    contact: string;
    email: string;
    address: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2: string;
    };
    verificationStatus: string;
    status: string;
  };
  farm?: {
    farmId: string;
    name: string;
    location: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2: string;
      coordinates?: {
        latitude: number;
        longitude: number;
      };
    };
    totalArea: number;
    soilType: string;
    waterSource: string;
    certifications: string[];
    farmingPractices: {
      primaryMethod: string;
      irrigationSystems: string[];
      sustainabilityScore?: number;
    };
  };
}

export interface SearchResult {
  products: EnrichedProduct[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export class ProductService {
  private productModel: typeof ProductModel;
  private syncService: SyncService;

  constructor() {
    this.productModel = ProductModel;
    this.syncService = new SyncService();
  }

  /**
   * Enrich products with farmer and farm information
   */
  private async enrichProductsWithFarmerInfo(products: ProductDocument[]): Promise<EnrichedProduct[]> {
    const enrichedProducts: EnrichedProduct[] = [];

    for (const product of products) {
      const enrichedProduct = product as EnrichedProduct;

      try {
        // Get farmer information using farmerId string (crops use farmerId field)
        logger.info(`Looking for farmer with farmerId: ${(product as any).farmerId}`);

        // Debug: Check total farmers count and sample data
        const totalFarmers = await FarmerModel.countDocuments();
        const sampleFarmers = await FarmerModel.find({}).limit(3).lean();
        logger.info('Farmers collection debug:', {
          totalFarmers,
          sampleFarmers: sampleFarmers.map(f => ({ farmerId: f.farmerId, name: f.personalInfo?.name }))
        });

        const farmer = await FarmerModel.findOne({ farmerId: (product as any).farmerId }).lean() as FarmerDocument;
        logger.info('Farmer query result:', {
          farmerExists: !!farmer,
          farmerType: typeof farmer,
          farmerKeys: farmer ? Object.keys(farmer) : null,
          farmer: farmer
        });

        if (farmer) {
          logger.info('Enriching product with farmer data:', {
            farmerId: farmer.farmerId,
            name: farmer.name
          });
          enrichedProduct.farmer = {
            farmerId: farmer.farmerId,
            name: farmer.name,
            contact: farmer.contact,
            email: farmer.email,
            address: farmer.address,
            verificationStatus: farmer.verificationStatus,
            status: farmer.status
          };
          logger.info('After setting farmer, enrichedProduct.farmer:', enrichedProduct.farmer);
        } else {
          logger.warn(`No farmer found for farmerId: ${(product as any).farmerId}`);
        }

        // Get farm information using farmId from product (crops have direct farmId field)
        const farmId = (product as any).farmId;
        logger.info(`Looking for farm with farmId: ${farmId}`);
        if (farmId) {
          const farm = await FarmModel.findOne({ farmId }).lean() as FarmDocument;
          logger.info('Found farm:', farm ? {
            _id: farm._id,
            farmId: farm.farmId,
            name: farm.name,
            location: farm.location
          } : null);
          if (farm) {
            enrichedProduct.farm = {
              farmId: farm.farmId,
              name: farm.name,
              location: farm.location,
              totalArea: farm.totalArea,
              soilType: farm.soilType,
              waterSource: farm.waterSource,
              certifications: farm.certifications,
              farmingPractices: farm.farmingPractices
            };
          }
        }
      } catch (error) {
        logger.warn(`Failed to enrich product ${product._id} with farmer/farm info:`, error);
        // Continue without farmer/farm info if there's an error
      }

      enrichedProducts.push(enrichedProduct);
    }

    logger.info(`Enrichment complete. Returning ${enrichedProducts.length} products. First product has farmer:`, {
      hasFarmer: !!enrichedProducts[0]?.farmer,
      farmerName: enrichedProducts[0]?.farmer?.name
    });

    return enrichedProducts;
  }

  async searchProducts(options: SearchOptions): Promise<SearchResult> {
    const { page = 1, limit = 20, query, filters } = options;
    const filter: FilterQuery<ProductDocument> = {};

    // Apply text search if query exists
    if (query) {
      filter.$text = { $search: query };
    }

    // Apply filters if they exist
    if (filters) {
      if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
        filter['price.amount'] = {};
        if (filters.minPrice !== undefined) filter['price.amount'].$gte = filters.minPrice;
        if (filters.maxPrice !== undefined) filter['price.amount'].$lte = filters.maxPrice;
      }

      if (filters.sellerId) {
        filter.sellerId = new mongoose.Types.ObjectId(filters.sellerId);
      }

      if (filters.categories?.length) {
        filter.category = { $in: filters.categories };
      }
    }

    try {
      const [products, total] = await Promise.all([
        this.productModel
          .find(filter)
          .skip((page - 1) * limit)
          .limit(limit)
          .lean()
          .exec(),
        this.productModel.countDocuments(filter)
      ]);

      // Enrich products with farmer and farm information
      const enrichedProducts = await this.enrichProductsWithFarmerInfo(products as ProductDocument[]);

      return {
        products: enrichedProducts,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('MongoDB search failed:', error);
      throw error;
    }
  }

  async findProductById(id: string, includeDeleted = false): Promise<EnrichedProduct | null> {
    const product = await this.productModel.findById(id).lean();
    if (!product) {
      return null;
    }

    // Enrich single product with farmer and farm information
    const enrichedProducts = await this.enrichProductsWithFarmerInfo([product as ProductDocument]);
    return enrichedProducts[0] || null;
  }

  async createProduct(productData: Partial<ProductDocument>): Promise<ProductDocument> {
    try {
      const product = await this.productModel.create(productData);
      await this.syncService.syncProduct(product);
      return product;
    } catch (error) {
      logger.error('Failed to create product:', error);
      throw error;
    }
  }

  async updateProduct(id: string, updateData: Partial<ProductDocument>): Promise<ProductDocument | null> {
    try {
      const product = await this.productModel.findByIdAndUpdate(id, updateData, { new: true });
      if (product) {
        await this.syncService.syncProduct(product);
      }
      return product;
    } catch (error) {
      logger.error('Failed to update product:', error);
      throw error;
    }
  }

  async deleteProduct(id: string): Promise<boolean> {
    try {
      const product = await this.productModel.findByIdAndDelete(id);
      if (product) {
        await this.syncService.deleteProduct(id);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Failed to delete product:', error);
      throw error;
    }
  }

  async syncAllProducts(): Promise<void> {
    try {
      await this.syncService.syncAllProducts();
    } catch (error) {
      logger.error('Failed to sync all products:', error);
      throw error;
    }
  }
}

export default ProductService;