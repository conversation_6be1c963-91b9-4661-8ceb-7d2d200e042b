import mongoose from 'mongoose';
import { config } from '../config';
import { logger } from '../utils/logger';

// Crop interface
interface ICrop {
  cropId: string;
  numberOfPlots: number;
  farmId: string;
  farmerId: string;
  name: string;
  type: string;
  variety: string;
  plantingDate: Date;
  expectedHarvestDate: Date;
  actualHarvestDate?: Date;
  growthStage: string;
  health: {
    status: string;
    issues: string[];
    lastCheck: Date;
  };
  yield: {
    expected: number;
    actual: number;
    unit: string;
  };
  resources: {
    water: number;
    fertilizer: number;
    pesticides: number;
  };
  soilConditions: {
    type: string;
    ph: number;
    nutrients: string[];
  };
  waterAvailability: string;
  metadata: {
    cropCategory: string;
    farmingMethod: string;
    irrigationMethod: string;
    harvestSeason: string;
    pestDiseaseStatus: string;
    storageMethod: string;
    nutrientManagement: string;
    waterSource: string;
    pesticideUsage: string;
    seedType: string;
    harvestingMethod: string;
  };
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fiber?: number;
    vitamins?: string[];
  };
  cultivation: {
    irrigationNeeds: string;
    fertilizerRequirements: string;
    pestControl: string;
    climateConditions: string;
  };
  postHarvest?: {
    storageRequirements: string;
    shelfLife: string;
    processingMethods: string[];
  };
  sustainability?: {
    waterUsage: string;
    carbonFootprint: string;
    pesticide: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Create crop schema for seeding
const CropSchema = new mongoose.Schema({}, { strict: false, collection: 'crops' });
const CropModel = mongoose.model<ICrop>('Crop', CropSchema);

// Sample crop data
const cropSeeds: Partial<ICrop>[] = [
  {
    cropId: 'CROP-001',
    numberOfPlots: 2,
    farmId: 'FARM-001',
    farmerId: 'FARMER-001',
    name: 'Basmati Rice',
    type: 'Cereal',
    variety: 'Basmati 370',
    plantingDate: new Date('2024-06-15T00:00:00Z'),
    expectedHarvestDate: new Date('2024-10-30T00:00:00Z'),
    growthStage: 'GROWING',
    health: {
      status: 'HEALTHY',
      issues: [],
      lastCheck: new Date()
    },
    yield: {
      expected: 6000,
      actual: 0,
      unit: 'kg'
    },
    resources: {
      water: 2500,
      fertilizer: 80,
      pesticides: 10
    },
    soilConditions: {
      type: 'Alluvial',
      ph: 6.8,
      nutrients: ['N', 'P', 'K']
    },
    waterAvailability: 'Canal',
    metadata: {
      cropCategory: 'Cereals',
      farmingMethod: 'Conventional',
      irrigationMethod: 'Flood Irrigation',
      harvestSeason: 'Kharif',
      pestDiseaseStatus: 'None',
      storageMethod: 'Warehouse Storage',
      nutrientManagement: 'Balanced Fertilizers',
      waterSource: 'Canal Water',
      pesticideUsage: 'Integrated Pest Management',
      seedType: 'Certified',
      harvestingMethod: 'Mechanical'
    },
    nutritionalInfo: {
      calories: 130,
      protein: 2.7,
      carbs: 28,
      fiber: 0.4,
      vitamins: ['B1', 'B3', 'B6']
    },
    cultivation: {
      irrigationNeeds: 'High',
      fertilizerRequirements: 'Moderate',
      pestControl: 'Integrated',
      climateConditions: 'Warm and Humid'
    },
    postHarvest: {
      storageRequirements: 'Dry, cool storage',
      shelfLife: '12 months',
      processingMethods: ['Milling', 'Polishing']
    },
    sustainability: {
      waterUsage: 'High',
      carbonFootprint: 'Medium',
      pesticide: 'Low'
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    cropId: 'CROP-002',
    numberOfPlots: 3,
    farmId: 'FARM-001',
    farmerId: 'FARMER-001',
    name: 'Wheat',
    type: 'Cereal',
    variety: 'HD-2967',
    plantingDate: new Date('2024-11-15T00:00:00Z'),
    expectedHarvestDate: new Date('2025-04-15T00:00:00Z'),
    growthStage: 'READY',
    health: {
      status: 'HEALTHY',
      issues: [],
      lastCheck: new Date()
    },
    yield: {
      expected: 4500,
      actual: 0,
      unit: 'kg'
    },
    resources: {
      water: 1200,
      fertilizer: 60,
      pesticides: 8
    },
    soilConditions: {
      type: 'Alluvial',
      ph: 7.2,
      nutrients: ['N', 'P', 'K']
    },
    waterAvailability: 'Canal',
    metadata: {
      cropCategory: 'Cereals',
      farmingMethod: 'Conventional',
      irrigationMethod: 'Sprinkler Irrigation',
      harvestSeason: 'Rabi',
      pestDiseaseStatus: 'None',
      storageMethod: 'Warehouse Storage',
      nutrientManagement: 'NPK Fertilizers',
      waterSource: 'Canal Water',
      pesticideUsage: 'Selective Application',
      seedType: 'High Yielding Variety',
      harvestingMethod: 'Mechanical'
    },
    nutritionalInfo: {
      calories: 340,
      protein: 13.2,
      carbs: 71.2,
      fiber: 12.2,
      vitamins: ['B1', 'B3', 'E']
    },
    cultivation: {
      irrigationNeeds: 'Moderate',
      fertilizerRequirements: 'High',
      pestControl: 'Integrated',
      climateConditions: 'Cool and Dry'
    },
    postHarvest: {
      storageRequirements: 'Dry storage',
      shelfLife: '12 months',
      processingMethods: ['Threshing', 'Milling']
    },
    sustainability: {
      waterUsage: 'Medium',
      carbonFootprint: 'Medium',
      pesticide: 'Low'
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    cropId: 'CROP-003',
    numberOfPlots: 1,
    farmId: 'FARM-002',
    farmerId: 'FARMER-002',
    name: 'Tomato',
    type: 'Vegetable',
    variety: 'Hybrid',
    plantingDate: new Date('2024-12-01T00:00:00Z'),
    expectedHarvestDate: new Date('2025-03-15T00:00:00Z'),
    growthStage: 'GROWING',
    health: {
      status: 'HEALTHY',
      issues: [],
      lastCheck: new Date()
    },
    yield: {
      expected: 2500,
      actual: 0,
      unit: 'kg'
    },
    resources: {
      water: 800,
      fertilizer: 40,
      pesticides: 15
    },
    soilConditions: {
      type: 'Loamy',
      ph: 6.5,
      nutrients: ['N', 'P', 'K', 'Ca']
    },
    waterAvailability: 'Drip Irrigation',
    metadata: {
      cropCategory: 'Vegetables',
      farmingMethod: 'Organic',
      irrigationMethod: 'Drip Irrigation',
      harvestSeason: 'Zaid',
      pestDiseaseStatus: 'None',
      storageMethod: 'Cold Storage',
      nutrientManagement: 'Organic Fertilizers',
      waterSource: 'Borewell',
      pesticideUsage: 'Organic',
      seedType: 'Hybrid',
      harvestingMethod: 'Manual'
    },
    nutritionalInfo: {
      calories: 18,
      protein: 0.9,
      carbs: 3.9,
      fiber: 1.2,
      vitamins: ['C', 'K', 'A']
    },
    cultivation: {
      irrigationNeeds: 'Regular',
      fertilizerRequirements: 'High',
      pestControl: 'Organic',
      climateConditions: 'Warm'
    },
    postHarvest: {
      storageRequirements: 'Cool, humid storage',
      shelfLife: '2-3 weeks',
      processingMethods: ['Fresh Sale', 'Processing']
    },
    sustainability: {
      waterUsage: 'Low',
      carbonFootprint: 'Low',
      pesticide: 'Organic'
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

/**
 * Seed crops to database
 */
async function seedCrops() {
  try {
    logger.info('Starting crop seeding...');
    
    // Connect to MongoDB
    await mongoose.connect(config.mongoUri);
    logger.info('Connected to MongoDB');
    
    // Clear existing crops
    await CropModel.deleteMany({});
    logger.info('Cleared existing crops');
    
    // Insert new crops
    const insertedCrops = await CropModel.insertMany(cropSeeds);
    logger.info(`Successfully seeded ${insertedCrops.length} crops`);
    
    // Log sample crops
    logger.info('Sample seeded crops:');
    insertedCrops.forEach(crop => {
      logger.info(`- ${crop.name} (${crop.variety}) - ${crop.yield.expected} ${crop.yield.unit}`);
    });
    
    logger.info('✅ Crop seeding completed successfully');
    
  } catch (error) {
    logger.error('❌ Crop seeding failed:', error);
    throw error;
  } finally {
    await mongoose.connection.close();
    logger.info('Database connection closed');
  }
}

// Run the seeding
if (require.main === module) {
  seedCrops()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

export { seedCrops };
