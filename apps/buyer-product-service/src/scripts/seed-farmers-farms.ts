import mongoose from 'mongoose';
import { config } from '../config';
import { logger } from '../utils/logger';
import { FarmerModel } from '../models/farmer.model';
import { FarmModel } from '../models/farm.model';

const sampleFarmers = [
  {
    _id: new mongoose.Types.ObjectId('68696d18216dc3acb1553da6'),
    farmerId: 'FARMER-001',
    name: '<PERSON><PERSON>',
    contact: '+91-9876543210',
    email: '<EMAIL>',
    address: {
      country: 'India',
      state: 'Punjab',
      city: 'Ludhiana',
      pincode: '141001',
      addressLine1: 'Village Khanna, Tehsil Ludhiana',
      addressLine2: 'Near Gurudwara'
    },
    verificationStatus: 'verified',
    status: 'active'
  },
  {
    _id: new mongoose.Types.ObjectId('68696d18216dc3acb1553da7'),
    farmerId: 'FARMER-002',
    name: '<PERSON><PERSON>',
    contact: '+91-9876543211',
    email: '<EMAIL>',
    address: {
      country: 'India',
      state: 'Gujarat',
      city: 'Ahmedabad',
      pincode: '380001',
      addressLine1: 'Village Sanand, Tehsil Sanand',
      addressLine2: 'Near Primary School'
    },
    verificationStatus: 'verified',
    status: 'active'
  },
  {
    _id: new mongoose.Types.ObjectId('68696d18216dc3acb1553da8'),
    farmerId: 'FARMER-003',
    name: 'Ramesh Singh',
    contact: '+91-9876543212',
    email: '<EMAIL>',
    address: {
      country: 'India',
      state: 'Maharashtra',
      city: 'Pune',
      pincode: '411001',
      addressLine1: 'Village Baramati, Tehsil Baramati',
      addressLine2: 'Near Market'
    },
    verificationStatus: 'verified',
    status: 'active'
  }
];

const sampleFarms = [
  {
    farmId: 'FARM-001',
    name: 'Green Valley Farm',
    location: {
      country: 'India',
      state: 'Punjab',
      city: 'Ludhiana',
      pincode: '141001',
      addressLine1: 'Village Khanna, Tehsil Ludhiana',
      addressLine2: 'Near Canal',
      coordinates: {
        latitude: 30.9010,
        longitude: 75.8573
      }
    },
    totalArea: 25.5,
    soilType: 'Alluvial',
    waterSource: 'Canal Water',
    certifications: ['Organic', 'Fair Trade'],
    farmingPractices: {
      primaryMethod: 'Conventional',
      irrigationSystems: ['Flood Irrigation', 'Sprinkler'],
      sustainabilityScore: 75
    }
  },
  {
    farmId: 'FARM-002',
    name: 'Sunrise Organic Farm',
    location: {
      country: 'India',
      state: 'Maharashtra',
      city: 'Pune',
      pincode: '411001',
      addressLine1: 'Village Baramati, Tehsil Baramati',
      addressLine2: 'Near Highway',
      coordinates: {
        latitude: 18.1124,
        longitude: 74.7749
      }
    },
    totalArea: 15.0,
    soilType: 'Loamy',
    waterSource: 'Borewell',
    certifications: ['Organic', 'Sustainable'],
    farmingPractices: {
      primaryMethod: 'Organic',
      irrigationSystems: ['Drip Irrigation'],
      sustainabilityScore: 90
    }
  }
];

async function seedFarmersAndFarms() {
  try {
    logger.info('Starting farmers and farms seeding...');

    // Connect to MongoDB
    await mongoose.connect(config.mongoUri);
    logger.info('Connected to MongoDB');

    // Clear existing data
    await FarmerModel.deleteMany({});
    await FarmModel.deleteMany({});
    logger.info('Cleared existing farmers and farms');

    // Insert farmers
    const farmers = await FarmerModel.insertMany(sampleFarmers);
    logger.info(`Successfully seeded ${farmers.length} farmers`);

    // Insert farms
    const farms = await FarmModel.insertMany(sampleFarms);
    logger.info(`Successfully seeded ${farms.length} farms`);

    logger.info('Sample seeded farmers:');
    farmers.forEach(farmer => {
      logger.info(`- ${farmer.name} (${farmer.farmerId}) - ${farmer.address.city}, ${farmer.address.state}`);
    });

    logger.info('Sample seeded farms:');
    farms.forEach(farm => {
      logger.info(`- ${farm.name} (${farm.farmId}) - ${farm.totalArea} acres in ${farm.location.city}, ${farm.location.state}`);
    });

    logger.info('✅ Farmers and farms seeding completed successfully');

  } catch (error) {
    logger.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    logger.info('Database connection closed');
  }
}

// Run the seeding
seedFarmersAndFarms();
