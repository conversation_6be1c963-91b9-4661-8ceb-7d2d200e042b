import mongoose from 'mongoose';
import { config } from '../config';
import { logger } from '../utils/logger';
import { ProductModel, ProductDocument, ProductType, CropGrowthStage, CropHealthStatus } from '../models/product.model';

// Import crop schema from shared library
interface ICrop {
  _id: mongoose.Types.ObjectId;
  cropId: string;
  farmId: string;
  farmerId: string;
  numberOfPlots: number;
  name: string;
  type: string;
  variety: string;
  plantingDate: Date;
  expectedHarvestDate: Date;
  actualHarvestDate?: Date;
  growthStage: string;
  health: {
    status: string;
    issues: string[];
    lastCheck: Date;
  };
  yield: {
    expected: number;
    actual: number;
    unit: string;
  };
  resources: {
    water: number;
    fertilizer: number;
    pesticides: number;
  };
  soilConditions: {
    type: string;
    ph: number;
    nutrients: string[];
  };
  waterAvailability: string;
  metadata: {
    cropCategory: string;
    farmingMethod: string;
    irrigationMethod: string;
    harvestSeason: string;
    pestDiseaseStatus: string;
    storageMethod: string;
    nutrientManagement: string;
    waterSource: string;
    pesticideUsage: string;
    seedType: string;
    harvestingMethod: string;
  };
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fiber?: number;
    vitamins?: string[];
  };
  cultivation: {
    irrigationNeeds: string;
    fertilizerRequirements: string;
    pestControl: string;
    climateConditions: string;
  };
  postHarvest?: {
    storageRequirements: string;
    shelfLife: string;
    processingMethods: string[];
  };
  sustainability?: {
    waterUsage: string;
    carbonFootprint: string;
    pesticide: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Create crop schema for querying
const CropSchema = new mongoose.Schema({}, { strict: false, collection: 'crops' });
const CropModel = mongoose.model<ICrop>('Crop', CropSchema);

/**
 * Convert crop to product format
 */
function convertCropToProduct(crop: ICrop): Partial<ProductDocument> {
  // Calculate price based on crop type and expected yield
  const basePrice = calculateBasePrice(crop.type, crop.metadata?.cropCategory);
  const pricePerUnit = basePrice * (1 + Math.random() * 0.3); // Add some variation

  return {
    type: ProductType.CROP,
    sellerId: mongoose.Types.ObjectId.isValid(crop.farmerId)
      ? new mongoose.Types.ObjectId(crop.farmerId)
      : new mongoose.Types.ObjectId(),
    name: `${crop.name} - ${crop.variety}`,
    description: `Fresh ${crop.name} (${crop.variety}) grown using ${crop.metadata?.farmingMethod || 'conventional'} farming methods. Expected harvest: ${crop.expectedHarvestDate.toDateString()}`,
    category: crop.metadata?.cropCategory || crop.type,
    subCategory: crop.variety,
    price: {
      amount: pricePerUnit,
      currency: 'INR',
      unit: crop.yield?.unit || 'kg'
    },
    availability: {
      status: determineAvailabilityStatus(crop.growthStage, crop.expectedHarvestDate),
      quantity: crop.yield?.expected || 0
    },
    attributes: {
      // Crop-specific attributes
      cropId: crop.cropId,
      cropCategory: crop.metadata?.cropCategory,
      variety: crop.variety,
      farmingMethod: crop.metadata?.farmingMethod,
      growthStage: crop.growthStage as CropGrowthStage,
      irrigationMethod: crop.metadata?.irrigationMethod,
      harvestSeason: crop.metadata?.harvestSeason,
      cropHealthStatus: crop.health?.status as CropHealthStatus,
      pestDiseaseStatus: crop.metadata?.pestDiseaseStatus,
      storageMethod: crop.metadata?.storageMethod,
      nutrientManagement: crop.metadata?.nutrientManagement,
      waterSource: crop.metadata?.waterSource,
      pesticideUsage: crop.metadata?.pesticideUsage,
      seedType: crop.metadata?.seedType,
      harvestingMethod: crop.metadata?.harvestingMethod,
      
      // Dates
      plantingDate: crop.plantingDate,
      expectedHarvestDate: crop.expectedHarvestDate,
      actualHarvestDate: crop.actualHarvestDate,
      
      // Health and yield info
      health: crop.health ? {
        status: crop.health.status as CropHealthStatus,
        issues: crop.health.issues,
        lastCheck: crop.health.lastCheck
      } : undefined,
      yield: crop.yield,
      resources: crop.resources,
      soilConditions: crop.soilConditions,
      waterAvailability: crop.waterAvailability,
      
      // Additional info
      nutritionalInfo: crop.nutritionalInfo,
      cultivation: crop.cultivation,
      postHarvest: crop.postHarvest,
      sustainability: crop.sustainability,
      
      // Farm info
      farmId: crop.farmId,
      
      // Default product attributes
      tags: [crop.type, crop.variety, crop.metadata?.farmingMethod || 'conventional'],
      images: [] // Will be populated later
    },
    certifications: [], // Will be populated based on farming method
    isActive: true
  };
}

/**
 * Calculate base price for crop type
 */
function calculateBasePrice(cropType: string, category?: string): number {
  const priceMap: Record<string, number> = {
    // Cereals (per kg)
    'Rice': 30,
    'Wheat': 25,
    'Maize': 20,
    'Barley': 22,
    
    // Vegetables (per kg)
    'Tomato': 40,
    'Onion': 35,
    'Potato': 25,
    'Cabbage': 30,
    'Cauliflower': 45,
    'Carrot': 50,
    'Brinjal': 35,
    'Okra': 60,
    
    // Fruits (per kg)
    'Mango': 80,
    'Apple': 120,
    'Banana': 40,
    'Orange': 60,
    'Grapes': 100,
    
    // Pulses (per kg)
    'Chickpea': 80,
    'Lentil': 90,
    'Black Gram': 100,
    'Green Gram': 85,
    
    // Spices (per kg)
    'Turmeric': 200,
    'Chili': 150,
    'Coriander': 180,
    'Cumin': 300,
    
    // Default
    'default': 40
  };

  return priceMap[cropType] || priceMap[category || 'default'] || priceMap['default'];
}

/**
 * Determine availability status based on growth stage and harvest date
 */
function determineAvailabilityStatus(growthStage: string, expectedHarvestDate: Date): 'AVAILABLE' | 'OUT_OF_STOCK' | 'LIMITED' {
  const now = new Date();
  const daysToHarvest = Math.ceil((expectedHarvestDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

  if (growthStage === 'HARVESTED') {
    return 'AVAILABLE';
  } else if (growthStage === 'READY' || daysToHarvest <= 7) {
    return 'AVAILABLE';
  } else if (daysToHarvest <= 30) {
    return 'LIMITED';
  } else {
    return 'LIMITED';
  }
}

/**
 * Main migration function
 */
async function migrateCropsToProducts() {
  try {
    logger.info('Starting crop to product migration...');
    
    // Connect to MongoDB
    await mongoose.connect(config.mongoUri);
    logger.info('Connected to MongoDB');
    
    // Get all crops
    const crops = await CropModel.find({}).lean();
    logger.info(`Found ${crops.length} crops to migrate`);
    
    if (crops.length === 0) {
      logger.warn('No crops found in database. Make sure crops are seeded first.');
      return;
    }
    
    // Clear existing products (optional - comment out if you want to keep existing products)
    await ProductModel.deleteMany({});
    logger.info('Cleared existing products');
    
    // Convert and insert products
    const products: Partial<ProductDocument>[] = [];
    
    for (const crop of crops) {
      try {
        const product = convertCropToProduct(crop);
        products.push(product);
      } catch (error) {
        logger.error(`Failed to convert crop ${crop.cropId}:`, error);
      }
    }
    
    if (products.length > 0) {
      const insertedProducts = await ProductModel.insertMany(products);
      logger.info(`Successfully migrated ${insertedProducts.length} crops to products`);
      
      // Log some sample products
      logger.info('Sample migrated products:');
      insertedProducts.slice(0, 3).forEach(product => {
        logger.info(`- ${product.name} (${product.category}) - ₹${product.price.amount}/${product.price.unit}`);
      });
    }
    
    logger.info('✅ Crop to product migration completed successfully');
    
  } catch (error) {
    logger.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await mongoose.connection.close();
    logger.info('Database connection closed');
  }
}

// Run the migration
if (require.main === module) {
  migrateCropsToProducts()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

export { migrateCropsToProducts };
