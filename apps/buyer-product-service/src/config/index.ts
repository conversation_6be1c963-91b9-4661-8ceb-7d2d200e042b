import { z } from 'zod';
import { config as dotenvConfig } from 'dotenv';

// Load environment variables
dotenvConfig();

// Environment variables schema
const envVarsSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  MONGODB_URI: z.string().min(1, { message: 'MONGODB_URI is required' }),
  ELASTICSEARCH_NODE: z.string().optional(),
  ELASTICSEARCH_USERNAME: z.string().optional(),
  ELASTICSEARCH_PASSWORD: z.string().optional(),
  CORS_ORIGIN: z.string().default('*'),
  CORS_METHODS: z.string().default('GET,POST,PUT,DELETE,PATCH'),
  CORS_ALLOWED_HEADERS: z.string().default('Content-Type,Authorization'),
  BCRYPT_ROUNDS: z.string().default('10'),
  RATE_LIMIT_WINDOW_MS: z.string().default('900000'),
  RATE_LIMIT_MAX_REQUESTS: z.string().default('100'),
  LOG_LEVEL: z.string().default('info'),
  PORT: z.string().default('3000'),
  AUTH_PORT: z.string().default('6001'),
  PRODUCT_PORT: z.string().default('6002'),
});

// Validate and transform environment variables
const envVars = envVarsSchema.parse(process.env);

// Configuration object
export const config = {
  env: envVars.NODE_ENV,
  port: parseInt(envVars.PORT, 10),
  auth_port: parseInt(envVars.AUTH_PORT, 10),
  product_port: parseInt(envVars.PRODUCT_PORT, 10),
  mongoUri: envVars.MONGODB_URI,
  elasticsearch: null,
  cors: {
    origin: envVars.CORS_ORIGIN,
    methods: envVars.CORS_METHODS.split(','),
    allowedHeaders: envVars.CORS_ALLOWED_HEADERS.split(','),
  },
  security: {
    bcryptRounds: parseInt(envVars.BCRYPT_ROUNDS, 10),
    rateLimits: {
      windowMs: parseInt(envVars.RATE_LIMIT_WINDOW_MS, 10),
      max: parseInt(envVars.RATE_LIMIT_MAX_REQUESTS, 10),
    },
  },
  logging: {
    level: envVars.LOG_LEVEL,
  },
} as const;

export default config; 