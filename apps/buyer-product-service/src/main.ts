/**
 * Buyer Product Service
 * Handles product management and search for buyers
 */

import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { config } from './config';
import { logger } from './utils/logger';
import { setupRoutes } from './api/routes';
import { setupMiddleware } from './api/middlewares';
import { errorHandler } from './api/middlewares/error.middleware';
import { initializeServices } from './app';

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Logging middleware
app.use(morgan('combined'));

// Compression middleware
app.use(compression());

// CORS
app.use(cors({
  origin: config.cors.origin || '*',
  methods: config.cors.methods || ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: config.cors.allowedHeaders || ['Content-Type', 'Authorization']
}));

// Body parsing middleware with security limits
app.use(express.json({
  limit: '10kb' // Limit body size to 10kb
}));

app.use(express.urlencoded({ 
  extended: true,
  limit: '10kb'
}));

// Setup middleware
setupMiddleware(app);

// Rate limiting
const limiter = rateLimit({
  windowMs: config.security.rateLimits.windowMs,
  max: config.security.rateLimits.max
});
app.use(limiter);

// Routes
setupRoutes(app);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    service: 'buyer-product-service',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use(errorHandler);

async function connectToMongoDB(): Promise<void> {
  await mongoose.connect(config.mongoUri);
}

async function disconnectFromMongoDB(): Promise<void> {
  await mongoose.disconnect();
}

// Start server
async function startServer() {
  try {
    // Initialize services
    await initializeServices();



    // Start the server
    app.listen(config.port, () => {
      logger.info(`Server is running on port ${config.port}`);
    });

    // Cleanup
    process.on('SIGTERM', async () => {
      logger.info('SIGTERM signal received');
      
      // Close MongoDB connection
      await disconnectFromMongoDB();
      logger.info('MongoDB connection closed');

      process.exit(0);
    });

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
