import mongoose, { Schema, Document } from 'mongoose';

// Address interface
export interface IAddress {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault?: boolean;
  label?: string; // e.g., 'Home', 'Work', 'Billing'
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

// Preferences interface
export interface IPreferences {
  language: string;
  currency: string;
  timezone: string;
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    marketing: boolean;
    orderUpdates: boolean;
    priceAlerts: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private' | 'friends';
    showEmail: boolean;
    showPhone: boolean;
    allowDataCollection: boolean;
  };
  delivery: {
    preferredTimeSlots: string[];
    specialInstructions?: string;
    contactPreference: 'phone' | 'email' | 'sms';
  };
}

// Business information for business buyers
export interface IBusinessInfo {
  companyName: string;
  businessType: string;
  taxId?: string;
  registrationNumber?: string;
  website?: string;
  industry: string;
  employeeCount?: number;
  annualRevenue?: number;
}

// Enhanced Profile interface
export interface IBuyerProfile extends Document {
  userId: mongoose.Schema.Types.ObjectId;
  
  // Basic Information
  firstName: string;
  lastName: string;
  displayName?: string;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  
  // Contact Information
  email: string;
  phoneNumber?: string;
  alternatePhone?: string;
  
  // Profile Media
  profilePicture?: string;
  coverImage?: string;
  
  // Addresses
  addresses: IAddress[];
  defaultAddressId?: string;
  
  // Business Information (for business buyers)
  accountType: 'individual' | 'business';
  businessInfo?: IBusinessInfo;
  
  // Preferences
  preferences: IPreferences;
  
  // Profile Status
  isVerified: boolean;
  verificationLevel: 'none' | 'email' | 'phone' | 'identity' | 'business';
  isActive: boolean;
  
  // Social/Professional
  bio?: string;
  interests: string[];
  
  // Metadata
  lastLoginAt?: Date;
  profileCompleteness: number; // 0-100
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

// Address Schema
const AddressSchema = new Schema<IAddress>({
  street: { type: String, required: true, trim: true, maxlength: 200 },
  city: { type: String, required: true, trim: true, maxlength: 100 },
  state: { type: String, required: true, trim: true, maxlength: 100 },
  postalCode: { type: String, required: true, trim: true, maxlength: 20 },
  country: { type: String, required: true, trim: true, maxlength: 100 },
  isDefault: { type: Boolean, default: false },
  label: { type: String, trim: true, maxlength: 50 },
  coordinates: {
    latitude: { type: Number, min: -90, max: 90 },
    longitude: { type: Number, min: -180, max: 180 }
  }
});

// Business Info Schema
const BusinessInfoSchema = new Schema<IBusinessInfo>({
  companyName: { type: String, required: true, trim: true, maxlength: 200 },
  businessType: { type: String, required: true, trim: true, maxlength: 100 },
  taxId: { type: String, trim: true, maxlength: 50 },
  registrationNumber: { type: String, trim: true, maxlength: 50 },
  website: { type: String, trim: true, maxlength: 200 },
  industry: { type: String, required: true, trim: true, maxlength: 100 },
  employeeCount: { type: Number, min: 1 },
  annualRevenue: { type: Number, min: 0 }
});

// Preferences Schema
const PreferencesSchema = new Schema<IPreferences>({
  language: { type: String, default: 'en', maxlength: 10 },
  currency: { type: String, default: 'USD', maxlength: 3 },
  timezone: { type: String, default: 'UTC', maxlength: 50 },
  notifications: {
    email: { type: Boolean, default: true },
    sms: { type: Boolean, default: false },
    push: { type: Boolean, default: true },
    marketing: { type: Boolean, default: false },
    orderUpdates: { type: Boolean, default: true },
    priceAlerts: { type: Boolean, default: false }
  },
  privacy: {
    profileVisibility: { 
      type: String, 
      enum: ['public', 'private', 'friends'], 
      default: 'private' 
    },
    showEmail: { type: Boolean, default: false },
    showPhone: { type: Boolean, default: false },
    allowDataCollection: { type: Boolean, default: true }
  },
  delivery: {
    preferredTimeSlots: [{ type: String }],
    specialInstructions: { type: String, maxlength: 500 },
    contactPreference: { 
      type: String, 
      enum: ['phone', 'email', 'sms'], 
      default: 'email' 
    }
  }
});

// Main Profile Schema
const BuyerProfileSchema = new Schema<IBuyerProfile>({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true, 
    unique: true, 
    index: true 
  },
  
  // Basic Information
  firstName: { type: String, required: true, trim: true, maxlength: 50 },
  lastName: { type: String, required: true, trim: true, maxlength: 50 },
  displayName: { type: String, trim: true, maxlength: 100 },
  dateOfBirth: { type: Date },
  gender: { 
    type: String, 
    enum: ['male', 'female', 'other', 'prefer_not_to_say'] 
  },
  
  // Contact Information
  email: { type: String, required: true, lowercase: true, trim: true },
  phoneNumber: { type: String, trim: true },
  alternatePhone: { type: String, trim: true },
  
  // Profile Media
  profilePicture: { type: String, trim: true },
  coverImage: { type: String, trim: true },
  
  // Addresses
  addresses: [AddressSchema],
  defaultAddressId: { type: String },
  
  // Business Information
  accountType: { 
    type: String, 
    enum: ['individual', 'business'], 
    default: 'individual',
    required: true 
  },
  businessInfo: BusinessInfoSchema,
  
  // Preferences
  preferences: { type: PreferencesSchema, default: () => ({}) },
  
  // Profile Status
  isVerified: { type: Boolean, default: false },
  verificationLevel: { 
    type: String, 
    enum: ['none', 'email', 'phone', 'identity', 'business'], 
    default: 'none' 
  },
  isActive: { type: Boolean, default: true },
  
  // Social/Professional
  bio: { type: String, maxlength: 1000 },
  interests: [{ type: String, trim: true, maxlength: 50 }],
  
  // Metadata
  lastLoginAt: { type: Date },
  profileCompleteness: { type: Number, default: 0, min: 0, max: 100 }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
BuyerProfileSchema.index({ email: 1 });
BuyerProfileSchema.index({ 'addresses.postalCode': 1 });
BuyerProfileSchema.index({ accountType: 1 });
BuyerProfileSchema.index({ isVerified: 1 });
BuyerProfileSchema.index({ createdAt: -1 });

// Virtual for full name
BuyerProfileSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Method to calculate profile completeness
BuyerProfileSchema.methods.calculateCompleteness = function(): number {
  let score = 0;
  const fields = [
    'firstName', 'lastName', 'email', 'phoneNumber', 
    'profilePicture', 'bio', 'dateOfBirth'
  ];
  
  fields.forEach(field => {
    if (this[field]) score += 10;
  });
  
  if (this.addresses && this.addresses.length > 0) score += 15;
  if (this.interests && this.interests.length > 0) score += 10;
  if (this.isVerified) score += 15;
  
  return Math.min(score, 100);
};

// Pre-save middleware to update profile completeness
BuyerProfileSchema.pre('save', function(next) {
  // Calculate profile completeness inline
  let score = 0;
  const fields = [
    'firstName', 'lastName', 'email', 'phoneNumber',
    'profilePicture', 'bio', 'dateOfBirth'
  ];

  fields.forEach(field => {
    if (this[field]) score += 10;
  });

  if (this.addresses && this.addresses.length > 0) score += 15;
  if (this.interests && this.interests.length > 0) score += 10;
  if (this.isVerified) score += 15;

  this.profileCompleteness = Math.min(score, 100);
  next();
});

export const BuyerProfileModel = mongoose.model<IBuyerProfile>('BuyerProfile', BuyerProfileSchema);
