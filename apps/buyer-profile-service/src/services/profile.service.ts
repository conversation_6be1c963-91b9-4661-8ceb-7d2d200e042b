import { BuyerProfileModel, IBuyerProfile, IAddress, IBusinessInfo, IPreferences } from '../models/profile.model';
import { UserModel } from '@agritech/shared';
import { AppError, createProfileError, createNotFoundError, createValidationError, Logger } from '../utils/error-handler';
import { ProfileEventPublisher } from '../events/publishers/profile-event-publisher';
import { CreateProfileInput, UpdateProfileInput } from '../validators/profile.validator';
import mongoose from 'mongoose';
export interface ProfileQueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  accountType?: 'individual' | 'business';
  isVerified?: boolean;
  city?: string;
  country?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class ProfileService {
  // Get profile by user ID
  static async getProfile(userId: string): Promise<IBuyerProfile | null> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId }).lean();
      return profile;
    } catch (error) {
      Logger.error('Error fetching profile', error, { userId });
      throw new AppError(500, 'Could not fetch profile');
    }
  }

  // Get profile by profile ID
  static async getProfileById(profileId: string): Promise<IBuyerProfile | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(profileId)) {
        throw createValidationError('Invalid profile ID format');
      }

      const profile = await BuyerProfileModel.findById(profileId).lean();
      return profile;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error fetching profile by ID', error, { profileId });
      throw new AppError(500, 'Could not fetch profile');
    }
  }

  // Create new profile
  static async createProfile(userId: string, email: string, profileData: CreateProfileInput): Promise<IBuyerProfile> {
    try {
      // Check if profile already exists
      const existingProfile = await BuyerProfileModel.findOne({ userId });
      if (existingProfile) {
        throw new AppError(409, 'Profile already exists for this user');
      }

      // Verify user exists (optional check)
      if (process.env.VERIFY_USER_EXISTS !== 'false') {
        const user = await UserModel.findById(userId);
        if (!user) {
          throw createNotFoundError('User');
        }
      }

      // Create profile
      const profile = new BuyerProfileModel({
        userId,
        email,
        ...profileData,
      });

      await profile.save();

      // Publish event
      await ProfileEventPublisher.publishProfileCreated(profile);

      Logger.info('Profile created successfully', { userId, profileId: profile._id });
      return profile;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error creating profile', error, { userId });
      throw new AppError(500, 'Could not create profile');
    }
  }

  // Update profile
  static async updateProfile(userId: string, profileData: UpdateProfileInput): Promise<IBuyerProfile> {
    try {
      const existingProfile = await BuyerProfileModel.findOne({ userId });
      if (!existingProfile) {
        throw createProfileError('Profile not found');
      }

      // Track changed fields for events
      const updatedFields: string[] = [];
      const previousValues: Record<string, any> = {};

      Object.keys(profileData).forEach(key => {
        if (profileData[key as keyof UpdateProfileInput] !== undefined) {
          updatedFields.push(key);
          previousValues[key] = existingProfile[key as keyof IBuyerProfile];
        }
      });

      // Track previous completeness for event
      const previousCompleteness = existingProfile.profileCompleteness;

      // Update profile
      const updatedProfile = await BuyerProfileModel.findOneAndUpdate(
        { userId },
        { $set: profileData },
        { new: true, runValidators: true }
      );

      if (!updatedProfile) {
        throw createProfileError('Profile update failed');
      }

      // Publish events
      await ProfileEventPublisher.publishProfileUpdated(updatedProfile, updatedFields, previousValues);

      // Check if completeness changed
      if (updatedProfile.profileCompleteness !== previousCompleteness) {
        await ProfileEventPublisher.publishProfileCompletenessChanged(
          userId,
          updatedProfile._id.toString(),
          previousCompleteness,
          updatedProfile.profileCompleteness
        );
      }

      Logger.info('Profile updated successfully', { userId, updatedFields });
      return updatedProfile;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error updating profile', error, { userId });
      throw new AppError(500, 'Could not update profile');
    }
  }

  // Delete profile
  static async deleteProfile(userId: string, reason?: string): Promise<void> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId });
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      await BuyerProfileModel.findOneAndDelete({ userId });

      // Publish event
      await ProfileEventPublisher.publishProfileDeleted(userId, profile._id.toString(), reason);

      Logger.info('Profile deleted successfully', { userId, reason });
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error deleting profile', error, { userId });
      throw new AppError(500, 'Could not delete profile');
    }
  }

  // Search profiles with pagination and filtering
  static async searchProfiles(options: ProfileQueryOptions): Promise<{
    profiles: IBuyerProfile[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        accountType,
        isVerified,
        city,
        country,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      // Build query
      const query: any = { isActive: true };

      if (search) {
        query.$or = [
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } },
          { displayName: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
        ];
      }

      if (accountType) {
        query.accountType = accountType;
      }

      if (isVerified !== undefined) {
        query.isVerified = isVerified;
      }

      if (city) {
        query['addresses.city'] = { $regex: city, $options: 'i' };
      }

      if (country) {
        query['addresses.country'] = { $regex: country, $options: 'i' };
      }

      // Build sort
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute query with pagination
      const skip = (page - 1) * limit;
      const [profiles, total] = await Promise.all([
        BuyerProfileModel.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        BuyerProfileModel.countDocuments(query)
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        profiles,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      Logger.error('Error searching profiles', error, { options });
      throw new AppError(500, 'Could not search profiles');
    }
  }

  // Update profile picture
  static async updateProfilePicture(userId: string, pictureUrl: string): Promise<IBuyerProfile> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId });
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      const previousPictureUrl = profile.profilePicture;

      const updatedProfile = await BuyerProfileModel.findOneAndUpdate(
        { userId },
        { $set: { profilePicture: pictureUrl } },
        { new: true, runValidators: true }
      );

      if (!updatedProfile) {
        throw new AppError(500, 'Failed to update profile picture');
      }

      // Publish event
      await ProfileEventPublisher.publishProfilePictureUpdated(
        userId,
        updatedProfile._id.toString(),
        pictureUrl,
        previousPictureUrl
      );

      Logger.info('Profile picture updated successfully', { userId, pictureUrl });
      return updatedProfile;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error updating profile picture', error, { userId });
      throw new AppError(500, 'Could not update profile picture');
    }
  }

  // Verify profile
  static async verifyProfile(
    userId: string,
    verificationLevel: 'email' | 'phone' | 'identity' | 'business',
    verifiedBy?: string
  ): Promise<IBuyerProfile> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId });
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      const updatedProfile = await BuyerProfileModel.findOneAndUpdate(
        { userId },
        {
          $set: {
            isVerified: true,
            verificationLevel: verificationLevel
          }
        },
        { new: true, runValidators: true }
      );

      if (!updatedProfile) {
        throw new AppError(500, 'Failed to verify profile');
      }

      // Publish event
      await ProfileEventPublisher.publishProfileVerified(
        userId,
        updatedProfile._id.toString(),
        verificationLevel,
        verifiedBy
      );

      Logger.info('Profile verified successfully', { userId, verificationLevel, verifiedBy });
      return updatedProfile;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error verifying profile', error, { userId });
      throw new AppError(500, 'Could not verify profile');
    }
  }
}