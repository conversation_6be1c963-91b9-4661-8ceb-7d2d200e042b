import { Buyer<PERSON><PERSON><PERSON>leModel, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../models/profile.model';
import { AppError, createProfileError, createNotFoundError, Logger } from '../utils/error-handler';
import { ProfileEventPublisher } from '../events/publishers/profile-event-publisher';
import { AddAddressInput, UpdateAddressInput } from '../validators/profile.validator';
import mongoose from 'mongoose';

export class AddressService {
  // Add new address to profile
  static async addAddress(userId: string, addressData: AddAddressInput): Promise<IBuyerProfile> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId });
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      // Check address limit (max 10 addresses)
      if (profile.addresses.length >= 10) {
        throw new AppError(400, 'Maximum number of addresses (10) reached');
      }

      // Validate required fields
      if (!addressData.street || !addressData.city || !addressData.state || !addressData.postalCode || !addressData.country) {
        throw new AppError(400, 'Missing required address fields: street, city, state, postalCode, country');
      }

      // Create proper IAddress object
      const newAddress: IAddress = {
        street: addressData.street,
        city: addressData.city,
        state: addressData.state,
        postalCode: addressData.postalCode,
        country: addressData.country,
        isDefault: addressData.isDefault || false,
        label: addressData.label,
        coordinates: addressData.coordinates && addressData.coordinates.latitude && addressData.coordinates.longitude
          ? {
              latitude: addressData.coordinates.latitude,
              longitude: addressData.coordinates.longitude
            }
          : undefined
      };

      // If this is the first address or marked as default, make it default
      if (profile.addresses.length === 0 || newAddress.isDefault) {
        // Remove default from other addresses
        profile.addresses.forEach(addr => {
          addr.isDefault = false;
        });
        newAddress.isDefault = true;
      }

      // Add new address
      profile.addresses.push(newAddress);
      await profile.save();

      // Publish event
      await ProfileEventPublisher.publishAddressAdded(newAddress, userId);

      Logger.info('Address added successfully', { userId, addressCount: profile.addresses.length });
      return profile;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error adding address', error, { userId });
      throw new AppError(500, 'Could not add address');
    }
  }

  // Update existing address
  static async updateAddress(
    userId: string,
    addressId: string,
    addressData: UpdateAddressInput
  ): Promise<IBuyerProfile> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId });
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      const addressIndex = profile.addresses.findIndex(
        addr => (addr as any)._id?.toString() === addressId
      );

      if (addressIndex === -1) {
        throw createNotFoundError('Address');
      }

      // Track updated fields
      const updatedFields: string[] = [];
      Object.keys(addressData).forEach(key => {
        if (addressData[key as keyof IAddress] !== undefined) {
          updatedFields.push(key);
        }
      });

      // If setting as default, remove default from others
      if (addressData.isDefault) {
        profile.addresses.forEach((addr, index) => {
          if (index !== addressIndex) {
            addr.isDefault = false;
          }
        });
      }

      // Update address
      Object.assign(profile.addresses[addressIndex], addressData);
      await profile.save();

      // Publish event
      await ProfileEventPublisher.publishAddressUpdated(
        profile.addresses[addressIndex],
        userId,
        updatedFields
      );

      Logger.info('Address updated successfully', { userId, addressId, updatedFields });
      return profile;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error updating address', error, { userId, addressId });
      throw new AppError(500, 'Could not update address');
    }
  }

  // Delete address
  static async deleteAddress(userId: string, addressId: string): Promise<IBuyerProfile> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId });
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      const addressIndex = profile.addresses.findIndex(
        addr => (addr as any)._id?.toString() === addressId
      );

      if (addressIndex === -1) {
        throw createNotFoundError('Address');
      }

      const addressToDelete = profile.addresses[addressIndex];
      const wasDefault = addressToDelete.isDefault;

      // Remove address
      profile.addresses.splice(addressIndex, 1);

      // If deleted address was default and there are other addresses, make the first one default
      if (wasDefault && profile.addresses.length > 0) {
        profile.addresses[0].isDefault = true;
        
        // Publish default address changed event
        await ProfileEventPublisher.publishDefaultAddressChanged(
          userId,
          (profile.addresses[0] as any)._id?.toString(),
          addressId
        );
      }

      await profile.save();

      // Publish event
      await ProfileEventPublisher.publishAddressDeleted(addressId, userId);

      Logger.info('Address deleted successfully', { userId, addressId, wasDefault });
      return profile;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error deleting address', error, { userId, addressId });
      throw new AppError(500, 'Could not delete address');
    }
  }

  // Set default address
  static async setDefaultAddress(userId: string, addressId: string): Promise<IBuyerProfile> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId });
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      const addressIndex = profile.addresses.findIndex(
        addr => (addr as any)._id?.toString() === addressId
      );

      if (addressIndex === -1) {
        throw createNotFoundError('Address');
      }

      // Find current default address
      const currentDefaultIndex = profile.addresses.findIndex(addr => addr.isDefault);
      const previousDefaultAddressId = currentDefaultIndex !== -1 
        ? (profile.addresses[currentDefaultIndex] as any)._id?.toString()
        : undefined;

      // Update default status
      profile.addresses.forEach((addr, index) => {
        addr.isDefault = index === addressIndex;
      });

      profile.defaultAddressId = addressId;
      await profile.save();

      // Publish event
      await ProfileEventPublisher.publishDefaultAddressChanged(
        userId,
        addressId,
        previousDefaultAddressId
      );

      Logger.info('Default address changed successfully', { 
        userId, 
        newDefaultAddressId: addressId,
        previousDefaultAddressId 
      });
      return profile;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error setting default address', error, { userId, addressId });
      throw new AppError(500, 'Could not set default address');
    }
  }

  // Get all addresses for a user
  static async getAddresses(userId: string): Promise<IAddress[]> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId }).select('addresses');
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      return profile.addresses;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error fetching addresses', error, { userId });
      throw new AppError(500, 'Could not fetch addresses');
    }
  }

  // Get specific address
  static async getAddress(userId: string, addressId: string): Promise<IAddress | null> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId }).select('addresses');
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      const address = profile.addresses.find(
        addr => (addr as any)._id?.toString() === addressId
      );

      return address || null;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error fetching address', error, { userId, addressId });
      throw new AppError(500, 'Could not fetch address');
    }
  }

  // Get default address
  static async getDefaultAddress(userId: string): Promise<IAddress | null> {
    try {
      const profile = await BuyerProfileModel.findOne({ userId }).select('addresses');
      if (!profile) {
        throw createProfileError('Profile not found');
      }

      const defaultAddress = profile.addresses.find(addr => addr.isDefault);
      return defaultAddress || null;
    } catch (error) {
      if (error instanceof AppError) throw error;
      Logger.error('Error fetching default address', error, { userId });
      throw new AppError(500, 'Could not fetch default address');
    }
  }
}
