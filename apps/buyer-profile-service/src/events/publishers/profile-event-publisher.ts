import { v4 as uuidv4 } from 'uuid';
import { IBuyerProfile, IAddress } from '../../models/profile.model';

// Simplified event data types
interface ProfileEventData {
  userId: string;
  profileId: string;
  firstName: string;
  lastName: string;
  email: string;
  accountType: string;
  isVerified: boolean;
  profileCompleteness: number;
}

interface AddressEventData {
  addressId: string;
  userId: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
  label?: string;
}

export class ProfileEventPublisher {
  private static mapProfileToEventData(profile: IBuyerProfile): ProfileEventData {
    return {
      userId: profile.userId.toString(),
      profileId: profile._id.toString(),
      firstName: profile.firstName,
      lastName: profile.lastName,
      email: profile.email,
      accountType: profile.accountType,
      isVerified: profile.isVerified,
      profileCompleteness: profile.profileCompleteness,
    };
  }

  private static mapAddressToEventData(address: IAddress, userId: string): AddressEventData {
    return {
      addressId: (address as any)._id?.toString() || uuidv4(),
      userId,
      street: address.street,
      city: address.city,
      state: address.state,
      postalCode: address.postalCode,
      country: address.country,
      isDefault: address.isDefault,
      label: address.label,
    };
  }

  static async publishProfileCreated(
    profile: IBuyerProfile,
    source?: string
  ): Promise<void> {
    try {
      // Simplified event logging instead of Kafka publishing
      console.log('[EVENT] ProfileCreated:', {
        profileId: profile._id?.toString(),
        userId: profile.userId?.toString(),
        firstName: profile.firstName,
        lastName: profile.lastName,
        source
      });
    } catch (error) {
      console.error('Failed to publish ProfileCreated event:', error);
    }
  }

  static async publishProfileUpdated(
    profile: IBuyerProfile,
    updatedFields: string[],
    previousValues?: Record<string, any>
  ): Promise<void> {
    try {
      console.log('[EVENT] ProfileUpdated:', {
        profileId: profile._id?.toString(),
        userId: profile.userId?.toString(),
        updatedFields
      });
    } catch (error) {
      console.error('Failed to publish ProfileUpdated event:', error);
    }
  }

  static async publishProfileDeleted(
    userId: string,
    profileId: string,
    reason?: string
  ): Promise<void> {
    try {
      console.log('[EVENT] ProfileDeleted:', {
        userId,
        profileId,
        reason
      });
    } catch (error) {
      console.error('Failed to publish ProfileDeleted event:', error);
    }
  }

  static async publishProfileVerified(
    userId: string,
    profileId: string,
    verificationLevel: 'email' | 'phone' | 'identity' | 'business',
    verifiedBy?: string
  ): Promise<void> {
    try {
      console.log('[EVENT] ProfileVerified:', {
        userId,
        profileId,
        verificationLevel,
        verifiedBy
      });
    } catch (error) {
      console.error('Failed to publish ProfileVerified event:', error);
    }
  }

  static async publishAddressAdded(
    address: IAddress,
    userId: string
  ): Promise<void> {
    try {
      console.log('[EVENT] AddressAdded:', {
        userId,
        addressId: (address as any)._id?.toString(),
        city: address.city,
        state: address.state
      });
    } catch (error) {
      console.error('Failed to publish AddressAdded event:', error);
    }
  }

  static async publishAddressUpdated(
    address: IAddress,
    userId: string,
    updatedFields: string[]
  ): Promise<void> {
    try {
      console.log('[EVENT] AddressUpdated:', {
        userId,
        addressId: (address as any)._id?.toString(),
        updatedFields
      });
    } catch (error) {
      console.error('Failed to publish AddressUpdated event:', error);
    }
  }

  static async publishAddressDeleted(
    addressId: string,
    userId: string
  ): Promise<void> {
    try {
      console.log('[EVENT] AddressDeleted:', {
        addressId,
        userId
      });
    } catch (error) {
      console.error('Failed to publish AddressDeleted event:', error);
    }
  }

  static async publishDefaultAddressChanged(
    userId: string,
    newDefaultAddressId: string,
    previousDefaultAddressId?: string
  ): Promise<void> {
    try {
      console.log('[EVENT] DefaultAddressChanged:', {
        userId,
        previousDefaultAddressId,
        newDefaultAddressId
      });
    } catch (error) {
      console.error('Failed to publish DefaultAddressChanged event:', error);
    }
  }

  static async publishProfilePictureUpdated(
    userId: string,
    profileId: string,
    newPictureUrl: string,
    previousPictureUrl?: string
  ): Promise<void> {
    try {
      console.log('[EVENT] ProfilePictureUpdated:', {
        userId,
        profileId,
        newPictureUrl
      });
    } catch (error) {
      console.error('Failed to publish ProfilePictureUpdated event:', error);
    }
  }

  static async publishBusinessInfoUpdated(
    userId: string,
    profileId: string,
    businessInfo: any
  ): Promise<void> {
    try {
      console.log('[EVENT] BusinessInfoUpdated:', {
        userId,
        profileId,
        companyName: businessInfo.companyName
      });
    } catch (error) {
      console.error('Failed to publish BusinessInfoUpdated event:', error);
    }
  }

  static async publishPreferencesUpdated(
    userId: string,
    profileId: string,
    updatedPreferences: Record<string, any>
  ): Promise<void> {
    try {
      console.log('[EVENT] PreferencesUpdated:', {
        userId,
        profileId,
        updatedKeys: Object.keys(updatedPreferences)
      });
    } catch (error) {
      console.error('Failed to publish PreferencesUpdated event:', error);
    }
  }

  static async publishProfileCompletenessChanged(
    userId: string,
    profileId: string,
    previousCompleteness: number,
    newCompleteness: number
  ): Promise<void> {
    try {
      console.log('[EVENT] ProfileCompletenessChanged:', {
        userId,
        profileId,
        previousCompleteness,
        newCompleteness
      });
    } catch (error) {
      console.error('Failed to publish ProfileCompletenessChanged event:', error);
    }
  }
}
