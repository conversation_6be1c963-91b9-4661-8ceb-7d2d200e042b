import dotenv from 'dotenv';
dotenv.config(); // Load .env file

// Basic configuration structure
// Replace placeholders with actual values or environment variables
export const config = {
  port: process.env.PORT,
  mongoUri: process.env.MONGODB_URI,
  cors: {
    origin: process.env.CORS_ORIGIN || '*', // Adjust for production
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
  },
  // Add other service-specific configurations if needed
}; 