import { z } from 'zod';

// Address validation schema
export const addressSchema = z.object({
  street: z.string()
    .min(1, 'Street address is required')
    .max(200, 'Street address must not exceed 200 characters')
    .trim(),
  city: z.string()
    .min(1, 'City is required')
    .max(100, 'City must not exceed 100 characters')
    .trim(),
  state: z.string()
    .min(1, 'State is required')
    .max(100, 'State must not exceed 100 characters')
    .trim(),
  postalCode: z.string()
    .min(1, 'Postal code is required')
    .max(20, 'Postal code must not exceed 20 characters')
    .regex(/^[A-Za-z0-9\s-]+$/, 'Invalid postal code format')
    .trim(),
  country: z.string()
    .min(1, 'Country is required')
    .max(100, 'Country must not exceed 100 characters')
    .trim(),
  isDefault: z.boolean().optional(),
  label: z.string()
    .max(50, 'Label must not exceed 50 characters')
    .trim()
    .optional(),
  coordinates: z.object({
    latitude: z.number().min(-90).max(90),
    longitude: z.number().min(-180).max(180)
  }).optional()
});

// Business info validation schema
export const businessInfoSchema = z.object({
  companyName: z.string()
    .min(1, 'Company name is required')
    .max(200, 'Company name must not exceed 200 characters')
    .trim(),
  businessType: z.string()
    .min(1, 'Business type is required')
    .max(100, 'Business type must not exceed 100 characters')
    .trim(),
  taxId: z.string()
    .max(50, 'Tax ID must not exceed 50 characters')
    .trim()
    .optional(),
  registrationNumber: z.string()
    .max(50, 'Registration number must not exceed 50 characters')
    .trim()
    .optional(),
  website: z.string()
    .url('Invalid website URL')
    .max(200, 'Website URL must not exceed 200 characters')
    .trim()
    .optional(),
  industry: z.string()
    .min(1, 'Industry is required')
    .max(100, 'Industry must not exceed 100 characters')
    .trim(),
  employeeCount: z.number()
    .int('Employee count must be an integer')
    .min(1, 'Employee count must be at least 1')
    .optional(),
  annualRevenue: z.number()
    .min(0, 'Annual revenue must be non-negative')
    .optional()
});

// Preferences validation schema
export const preferencesSchema = z.object({
  language: z.string()
    .max(10, 'Language code must not exceed 10 characters')
    .default('en'),
  currency: z.string()
    .length(3, 'Currency code must be exactly 3 characters')
    .default('USD'),
  timezone: z.string()
    .max(50, 'Timezone must not exceed 50 characters')
    .default('UTC'),
  notifications: z.object({
    email: z.boolean().default(true),
    sms: z.boolean().default(false),
    push: z.boolean().default(true),
    marketing: z.boolean().default(false),
    orderUpdates: z.boolean().default(true),
    priceAlerts: z.boolean().default(false)
  }).default({}),
  privacy: z.object({
    profileVisibility: z.enum(['public', 'private', 'friends']).default('private'),
    showEmail: z.boolean().default(false),
    showPhone: z.boolean().default(false),
    allowDataCollection: z.boolean().default(true)
  }).default({}),
  delivery: z.object({
    preferredTimeSlots: z.array(z.string()).default([]),
    specialInstructions: z.string()
      .max(500, 'Special instructions must not exceed 500 characters')
      .optional(),
    contactPreference: z.enum(['phone', 'email', 'sms']).default('email')
  }).default({})
}).default({});

// Phone number validation
const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;

// Create profile validation schema
export const createProfileSchema = z.object({
  body: z.object({
    firstName: z.string()
      .min(2, 'First name must be at least 2 characters')
      .max(50, 'First name must not exceed 50 characters')
      .regex(/^[a-zA-Z\s\'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
      .trim(),
    lastName: z.string()
      .min(2, 'Last name must be at least 2 characters')
      .max(50, 'Last name must not exceed 50 characters')
      .regex(/^[a-zA-Z\s\'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
      .trim(),
    displayName: z.string()
      .max(100, 'Display name must not exceed 100 characters')
      .trim()
      .optional(),
    dateOfBirth: z.string()
      .datetime()
      .transform(str => new Date(str))
      .refine(date => {
        const age = (Date.now() - date.getTime()) / (365.25 * 24 * 60 * 60 * 1000);
        return age >= 13 && age <= 120;
      }, 'Age must be between 13 and 120 years')
      .optional(),
    gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say']).optional(),
    phoneNumber: z.string()
      .regex(phoneRegex, 'Invalid phone number format')
      .optional(),
    alternatePhone: z.string()
      .regex(phoneRegex, 'Invalid alternate phone number format')
      .optional(),
    accountType: z.enum(['individual', 'business']).default('individual'),
    businessInfo: businessInfoSchema.optional(),
    addresses: z.array(addressSchema).default([]),
    bio: z.string()
      .max(1000, 'Bio must not exceed 1000 characters')
      .optional(),
    interests: z.array(
      z.string()
        .max(50, 'Interest must not exceed 50 characters')
        .trim()
    ).max(20, 'Maximum 20 interests allowed').default([]),
    preferences: preferencesSchema.optional()
  })
});

// Update profile validation schema (all fields optional)
export const updateProfileSchema = z.object({
  body: z.object({
    firstName: z.string()
      .min(2, 'First name must be at least 2 characters')
      .max(50, 'First name must not exceed 50 characters')
      .regex(/^[a-zA-Z\s\'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
      .trim()
      .optional(),
    lastName: z.string()
      .min(2, 'Last name must be at least 2 characters')
      .max(50, 'Last name must not exceed 50 characters')
      .regex(/^[a-zA-Z\s\'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
      .trim()
      .optional(),
    displayName: z.string()
      .max(100, 'Display name must not exceed 100 characters')
      .trim()
      .optional(),
    dateOfBirth: z.string()
      .datetime()
      .transform(str => new Date(str))
      .refine(date => {
        const age = (Date.now() - date.getTime()) / (365.25 * 24 * 60 * 60 * 1000);
        return age >= 13 && age <= 120;
      }, 'Age must be between 13 and 120 years')
      .optional(),
    gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say']).optional(),
    phoneNumber: z.string()
      .regex(phoneRegex, 'Invalid phone number format')
      .optional(),
    alternatePhone: z.string()
      .regex(phoneRegex, 'Invalid alternate phone number format')
      .optional(),
    bio: z.string()
      .max(1000, 'Bio must not exceed 1000 characters')
      .optional(),
    interests: z.array(
      z.string()
        .max(50, 'Interest must not exceed 50 characters')
        .trim()
    ).max(20, 'Maximum 20 interests allowed').optional()
  })
});

// Add address validation schema
export const addAddressSchema = z.object({
  body: addressSchema
});

// Update address validation schema
export const updateAddressSchema = z.object({
  body: z.object({
    street: z.string()
      .min(1, 'Street address is required')
      .max(200, 'Street address must not exceed 200 characters')
      .trim()
      .optional(),
    city: z.string()
      .min(1, 'City is required')
      .max(100, 'City must not exceed 100 characters')
      .trim()
      .optional(),
    state: z.string()
      .min(1, 'State is required')
      .max(100, 'State must not exceed 100 characters')
      .trim()
      .optional(),
    postalCode: z.string()
      .min(1, 'Postal code is required')
      .max(20, 'Postal code must not exceed 20 characters')
      .regex(/^[A-Za-z0-9\s-]+$/, 'Invalid postal code format')
      .trim()
      .optional(),
    country: z.string()
      .min(1, 'Country is required')
      .max(100, 'Country must not exceed 100 characters')
      .trim()
      .optional(),
    isDefault: z.boolean().optional(),
    label: z.string()
      .max(50, 'Label must not exceed 50 characters')
      .trim()
      .optional(),
    coordinates: z.object({
      latitude: z.number().min(-90).max(90).optional(),
      longitude: z.number().min(-180).max(180).optional()
    }).optional()
  })
});

// Update business info validation schema
export const updateBusinessInfoSchema = z.object({
  body: businessInfoSchema.partial()
});

// Update preferences validation schema
export const updatePreferencesSchema = z.object({
  body: z.object({
    language: z.string()
      .max(10, 'Language code must not exceed 10 characters')
      .optional(),
    currency: z.string()
      .length(3, 'Currency code must be exactly 3 characters')
      .optional(),
    timezone: z.string()
      .max(50, 'Timezone must not exceed 50 characters')
      .optional(),
    notifications: z.object({
      email: z.boolean().optional(),
      sms: z.boolean().optional(),
      push: z.boolean().optional(),
      marketing: z.boolean().optional(),
      orderUpdates: z.boolean().optional(),
      priceAlerts: z.boolean().optional()
    }).optional(),
    privacy: z.object({
      profileVisibility: z.enum(['public', 'private', 'friends']).optional(),
      showEmail: z.boolean().optional(),
      showPhone: z.boolean().optional(),
      allowDataCollection: z.boolean().optional()
    }).optional(),
    delivery: z.object({
      preferredTimeSlots: z.array(z.string()).optional(),
      specialInstructions: z.string()
        .max(500, 'Special instructions must not exceed 500 characters')
        .optional(),
      contactPreference: z.enum(['phone', 'email', 'sms']).optional()
    }).optional()
  })
});

// Profile picture upload validation
export const profilePictureSchema = z.object({
  body: z.object({
    profilePicture: z.string()
      .url('Invalid profile picture URL')
      .optional()
  })
});

// Query parameters validation for profile search/filtering
export const profileQuerySchema = z.object({
  query: z.object({
    page: z.string()
      .transform(val => parseInt(val, 10))
      .refine(val => val > 0, 'Page must be a positive number')
      .default('1'),
    limit: z.string()
      .transform(val => parseInt(val, 10))
      .refine(val => val > 0 && val <= 100, 'Limit must be between 1 and 100')
      .default('10'),
    search: z.string()
      .max(100, 'Search term must not exceed 100 characters')
      .optional(),
    accountType: z.enum(['individual', 'business']).optional(),
    isVerified: z.string()
      .transform(val => val === 'true')
      .optional(),
    city: z.string()
      .max(100, 'City must not exceed 100 characters')
      .optional(),
    country: z.string()
      .max(100, 'Country must not exceed 100 characters')
      .optional()
  })
});

// Type exports
export type CreateProfileInput = z.infer<typeof createProfileSchema>['body'];
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>['body'];
export type AddAddressInput = z.infer<typeof addAddressSchema>['body'];
export type UpdateAddressInput = z.infer<typeof updateAddressSchema>['body'];
export type UpdateBusinessInfoInput = z.infer<typeof updateBusinessInfoSchema>['body'];
export type UpdatePreferencesInput = z.infer<typeof updatePreferencesSchema>['body'];
export type ProfileQueryInput = z.infer<typeof profileQuerySchema>['query'];
