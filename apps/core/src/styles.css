@tailwind base;
@tailwind components;
@tailwind utilities;
/* You can add global styles to this file, and also import other style files */

:root {
  /* Primary colors */
  --color-primary: #22C55E;
  --color-primary-light: #34D399;
  --color-primary-dark: #15803D;
  --color-primary-contrast: #FFFFFF;
  
  /* Secondary colors */
  --color-secondary: #F97316;
  --color-secondary-light: #FB923C;
  --color-secondary-dark: #EA580C;
  --color-secondary-contrast: #FFFFFF;
  
  /* Background colors */
  --color-bg-default: #F0FDF4;
  --color-bg-paper: #FFFFFF;
  --color-bg-elevated: #DCFCE7;
  
  /* Text colors */
  --color-text-primary: #064E3B;
  --color-text-secondary: #374151;
  --color-text-disabled: #6B7280;
  
  /* Status colors */
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;
  
  /* Border colors */
  --color-border: #D1D5DB;
}

body {
  background-color: var(--color-bg-default);
  color: var(--color-text-primary);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Custom utility classes */
.btn-primary {
  @apply px-4 py-2 rounded;
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  @apply px-4 py-2 rounded border;
  background-color: transparent;
  border-color: var(--color-secondary);
  color: var(--color-secondary);
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--color-secondary);
  color: var(--color-bg-default);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary-dark);
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-secondary {
  background-color: var(--color-secondary);
}

.border-primary {
  border-color: var(--color-primary);
}

.hover-bg-primary:hover {
  background-color: var(--color-primary-light);
}

.hover-text-primary:hover {
  color: var(--color-primary-light);
}

/* Card styles */
.card {
  @apply rounded-lg overflow-hidden;
  background-color: var(--color-bg-paper);
  border: 1px solid var(--color-border);
  box-shadow: 0 4px 6px rgba(34, 197, 94, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 15px rgba(34, 197, 94, 0.15);
}

/* Input styles */
.input-field {
  @apply px-4 py-2 rounded border w-full;
  background-color: var(--color-bg-elevated);
  border-color: var(--color-border);
  color: var(--color-text-primary);
}

.input-field:focus {
  @apply outline-none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.25);
}
