import React from 'react'
import { Link } from 'react-router-dom'

interface CropCardProps {
  id: string
  name: string
  type: string
  price: number
  season: string
  certification: string
  stockStatus: 'In Stock' | 'Low Stock' | 'Out of Stock'
  imageUrl: string
  plotSize?: string
}

export const CropCard: React.FC<CropCardProps> = ({
  id,
  name,
  type,
  price,
  season,
  certification,
  stockStatus,
  imageUrl,
  plotSize = '100 sq.m.'
}) => {
  // Certification badge
  const certificationClassName = 
    "absolute top-2 right-2 text-xs px-3 py-1 rounded-full text-white " + 
    (certification === 'Organic' 
      ? "bg-[#6f0aa1]" 
      : certification === 'Non-GMO' 
        ? "bg-[#6f0aa1]" 
        : "bg-blue-700");
  
  // Stock status badge
  const stockClassName = 
    "text-xs px-2 py-1 rounded-full " + 
    (stockStatus === 'In Stock' 
      ? "bg-[#6f0aa1] text-white" 
      : stockStatus === 'Low Stock' 
        ? "bg-yellow-500 text-white" 
        : "bg-red-500 text-white");

  return (
    <div className="bg-[#1a1625] rounded-lg overflow-hidden shadow-md">
      <Link to={`/crops/${id}`} className="block relative">
        <img
          src={imageUrl || "https://via.placeholder.com/300x200"}
          alt={name}
          className="w-full h-48 object-cover"
        />
        <span className={certificationClassName}>
          {certification}
        </span>
      </Link>
      
      <div className="p-4">
        <Link to={`/crops/${id}`} className="block">
          <h3 className="text-lg font-medium text-white mb-1">{name}</h3>
          <div className="flex justify-between items-center mt-1 mb-1">
            <span className="text-gray-400">{type}</span>
            <span className="text-gray-400">{season}</span>
          </div>
          
          <div className="mb-3">
            <p className="text-xs text-gray-400">Plot size: {plotSize}</p>
          </div>
          
          <div className="flex justify-between items-center mb-4">
            <span className="text-xl font-bold text-white">${price.toFixed(2)}/plot</span>
            <span className={stockClassName}>{stockStatus}</span>
          </div>
        </Link>
      </div>
    </div>
  )
} 