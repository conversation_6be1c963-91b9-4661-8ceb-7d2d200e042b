import React from 'react'
import { Link } from 'react-router-dom'

export default function LandingPage() {
  return (
    <div className="bg-bg-default min-h-screen">
      {/* Hero Section */}
      <div className="container mx-auto px-6 py-20">
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 text-center md:text-left mb-10 md:mb-0">
            <h1 className="text-5xl font-bold text-white mb-4">Experience Agriculture, Harvest the Rewards</h1>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Connect with real farms and sustainable agriculture. Support expert farmers and enjoy fresh harvests.
            </p>
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center md:justify-start">
              <Link to="/auth/login" className="btn-primary px-8 py-3 rounded-full text-lg font-medium transition-all hover:scale-105">
                Start Farming
              </Link>
              <Link to="/auth/register" className="btn-secondary px-8 py-3 rounded-full text-lg font-medium transition-all hover:scale-105">
                Explore Farms
              </Link>
            </div>
          </div>
          <div className="md:w-1/2">
            <div className="bg-white bg-opacity-5 p-6 rounded-lg backdrop-blur-sm shadow-xl">
              <img src="/images/farm-tech.svg" alt="AgriTech illustration" className="w-full h-auto" />
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="bg-white bg-opacity-5 py-12">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div className="p-4">
              <div className="text-4xl font-bold text-white mb-2">5K+</div>
              <div className="text-gray-300">Community Members</div>
            </div>
            <div className="p-4">
              <div className="text-4xl font-bold text-white mb-2">15%</div>
              <div className="text-gray-300">Yield Increase</div>
            </div>
            <div className="p-4">
              <div className="text-4xl font-bold text-white mb-2">200+</div>
              <div className="text-gray-300">Verified Farmers</div>
            </div>
            <div className="p-4">
              <div className="text-4xl font-bold text-white mb-2">30K+</div>
              <div className="text-gray-300">Crops Delivered</div>
            </div>
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="py-16">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-white mb-12">How Agricultural Partnership Works</h2>
          
          <div className="flex flex-col md:flex-row items-center justify-center">
            <div className="flex flex-col items-center md:w-1/4 p-4">
              <div className="bg-white bg-opacity-10 w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mb-4">1</div>
              <h3 className="text-xl font-bold text-white mb-2">Select Your Plot</h3>
              <p className="text-center text-gray-300">Browse verified farmland and select the plots you wish to cultivate</p>
            </div>
            
            <div className="hidden md:block text-gray-500 text-2xl px-4">→</div>
            
            <div className="flex flex-col items-center md:w-1/4 p-4">
              <div className="bg-white bg-opacity-10 w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mb-4">2</div>
              <h3 className="text-xl font-bold text-white mb-2">Start Cultivation</h3>
              <p className="text-center text-gray-300">Begin your farming journey with expert guidance and support</p>
            </div>
            
            <div className="hidden md:block text-gray-500 text-2xl px-4">→</div>
            
            <div className="flex flex-col items-center md:w-1/4 p-4">
              <div className="bg-white bg-opacity-10 w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mb-4">3</div>
              <h3 className="text-xl font-bold text-white mb-2">Monitor Growth</h3>
              <p className="text-center text-gray-300">Track your crops' progress with real-time updates and expert insights</p>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-bg-default bg-opacity-70 py-16">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-white mb-12">Why Join Our Farming Community</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-white bg-opacity-5 rounded-xl p-6 shadow-lg transition-transform hover:transform hover:scale-105">
              <div className="text-gray-200 text-4xl mb-4">
                <i className="fas fa-hand-holding-usd"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Zero Farming Experience Needed</h3>
              <p className="text-gray-300">
                Expert farmers do all the work while you enjoy fresh harvests. No agricultural knowledge required.
              </p>
            </div>
            
            {/* Feature 2 */}
            <div className="bg-white bg-opacity-5 rounded-xl p-6 shadow-lg transition-transform hover:transform hover:scale-105">
              <div className="text-gray-200 text-4xl mb-4">
                <i className="fas fa-leaf"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Support Sustainable Agriculture</h3>
              <p className="text-gray-300">
                Make a positive impact by supporting eco-friendly farming practices that benefit the environment.
              </p>
            </div>
            
            {/* Feature 3 */}
            <div className="bg-white bg-opacity-5 rounded-xl p-6 shadow-lg transition-transform hover:transform hover:scale-105">
              <div className="text-gray-200 text-4xl mb-4">
                <i className="fas fa-carrot"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Transparent Farm-to-Table</h3>
              <p className="text-gray-300">
                Know exactly where your food comes from with complete traceability and farmer profiles.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Investment Options */}
      <div className="py-16">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-white mb-12">Agricultural Cultivation Options</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white bg-opacity-5 rounded-xl p-6 shadow-lg text-center">
              <div className="text-gray-200 text-4xl mb-4">
                <i className="fas fa-seedling"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Seasonal Crops</h3>
              <p className="text-gray-300 mb-4">
                Cultivate seasonal crops with expert guidance and modern farming techniques
              </p>
              <div className="text-2xl font-bold text-[#22C55E] mb-6">3-4 months cycle</div>
              <Link to="/crops" className="bg-[#F97316] hover:bg-[#F97316]/90 text-white px-6 py-2 rounded-full text-sm inline-block">
                Browse Crops
              </Link>
            </div>
            
            <div className="bg-white bg-opacity-10 rounded-xl p-6 shadow-lg text-center border border-gray-500">
              <div className="text-gray-200 text-4xl mb-4">
                <i className="fas fa-tractor"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Full Farm Management</h3>
              <p className="text-gray-300 mb-4">
                Take control of entire farm operations with comprehensive management tools
              </p>
              <div className="text-2xl font-bold text-[#22C55E] mb-6">Year-round farming</div>
              <Link to="/farms" className="bg-[#F97316] hover:bg-[#F97316]/90 text-white px-6 py-2 rounded-full text-sm inline-block">
                Explore Farms
              </Link>
            </div>
            
            <div className="bg-white bg-opacity-5 rounded-xl p-6 shadow-lg text-center">
              <div className="text-gray-200 text-4xl mb-4">
                <i className="fas fa-warehouse"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Community Farming</h3>
              <p className="text-gray-300 mb-4">
                Join a collaborative farming community supported by agricultural experts
              </p>
              <div className="text-2xl font-bold text-[#22C55E] mb-6">Flexible duration</div>
              <Link to="/community" className="bg-[#F97316] hover:bg-[#F97316]/90 text-white px-6 py-2 rounded-full text-sm inline-block">
                Join Community
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Testimonials */}
      <div className="bg-white bg-opacity-5 py-16">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-white mb-12">What Our Community Says</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white bg-opacity-5 p-6 rounded-lg">
              <div className="text-gray-300 italic mb-4">
                "I've always wanted to connect with where my food comes from. With AgriTech, I can support farmers directly and actually own the produce I receive. It's amazing!"
              </div>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-500 rounded-full mr-4"></div>
                <div>
                  <div className="text-white font-medium">Emma Chen</div>
                  <div className="text-gray-400 text-sm">Urban Investor, New York</div>
                </div>
              </div>
            </div>
            
            <div className="bg-white bg-opacity-5 p-6 rounded-lg">
              <div className="text-gray-300 italic mb-4">
                "As someone with no farming experience, I never thought I could participate in agriculture. AgriTech made it easy and transparent. I love watching my crops grow through the app."
              </div>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-500 rounded-full mr-4"></div>
                <div>
                  <div className="text-white font-medium">David Parker</div>
                  <div className="text-gray-400 text-sm">Tech Professional, San Francisco</div>
                </div>
              </div>
            </div>
            
            <div className="bg-white bg-opacity-5 p-6 rounded-lg">
              <div className="text-gray-300 italic mb-4">
                "The satisfaction from my farm participation has been incredible this year, plus I get fresh organic produce. It's a win-win experience that feels good."
              </div>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-500 rounded-full mr-4"></div>
                <div>
                  <div className="text-white font-medium">Sophia Williams</div>
                  <div className="text-gray-400 text-sm">Financial Analyst, Chicago</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter */}
      <div className="py-16">
        <div className="container mx-auto px-6">
          <div className="bg-white bg-opacity-5 p-8 rounded-lg max-w-3xl mx-auto">
            <h2 className="text-2xl font-bold text-white mb-4 text-center">Join Our Farming Community</h2>
            <p className="text-gray-300 mb-6 text-center">
              Subscribe to receive updates on new farm opportunities, harvest reports, and agricultural insights
            </p>
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0">
              <input
                type="email"
                placeholder="Your email address"
                className="flex-grow px-4 py-3 rounded-l-lg sm:rounded-r-none rounded-r-lg sm:rounded-l-lg bg-white bg-opacity-10 text-white border border-gray-700 focus:outline-none focus:border-gray-400"
              />
              <button className="btn-primary px-6 py-3 rounded-r-lg sm:rounded-l-none rounded-l-lg sm:rounded-r-lg">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="container mx-auto px-6 py-16 text-center">
        <h2 className="text-3xl font-bold text-white mb-6">Become a Farm Partner Today</h2>
        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
          Join thousands of members already participating in and supporting sustainable agriculture
        </p>
        <Link to="/auth/register" className="btn-primary px-10 py-4 rounded-full text-lg font-medium inline-block transition-all hover:scale-105">
          Create Your Account
        </Link>
      </div>

    </div>
  )
} 