import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { getAuthService } from '@shared'

export default function Login() {
  const [loginMethod, setLoginMethod] = useState<'password' | 'otp'>('password');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const navigate = useNavigate();

  const handlePasswordLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const authService = getAuthService();
      const response = await authService.login({ email, password });

      // Store tokens using the auth service
      authService.setTokens(response.tokens.accessToken, response.tokens.refreshToken);

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (err: any) {
      setError(err.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSendOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!phoneNumber) {
      setError('Please enter your phone number');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      // Simulated API call to send OTP
      // Replace with actual API call
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to send OTP');
      }
      
      setOtpSent(true);
    } catch (err: any) {
      setError(err.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!phoneNumber || !otp) {
      setError('Please enter the OTP');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      // Simulated API call to verify OTP
      // Replace with actual API call
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber, otp }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Invalid OTP');
      }
      
      // Store token in localStorage or secure cookie
      localStorage.setItem('authToken', data.token);
      
      // Redirect to dashboard
      navigate('/dashboard');
    } catch (err: any) {
      setError(err.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resendOtp = async () => {
    try {
      setLoading(true);
      setError('');
      
      // Simulated API call to resend OTP
      // Replace with actual API call
      const response = await fetch('/api/auth/resend-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to resend OTP');
      }
      
    } catch (err: any) {
      setError(err.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-bg-default">
      <div className="bg-bg-paper p-8 rounded-xl shadow-xl w-full max-w-md border border-white/10">
        <div className="flex justify-center mb-4">
          <h1 className="text-3xl font-bold text-white flex items-center">
            <span className="text-secondary mr-1 font-extrabold">Agri</span>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-secondary to-secondary-light">Tech</span>
          </h1>
        </div>
        <h2 className="text-xl font-medium mb-6 text-center text-white">Welcome Back</h2>
        
        {/* Login Method Selector */}
        <div className="bg-white/5 rounded-lg p-1 flex mb-6">
          <button
            onClick={() => setLoginMethod('password')}
            className={`flex-1 py-2 rounded-md text-sm font-medium transition-all ${
              loginMethod === 'password' 
                ? 'bg-white/10 text-white' 
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Email & Password
          </button>
          <button
            onClick={() => setLoginMethod('otp')}
            className={`flex-1 py-2 rounded-md text-sm font-medium transition-all ${
              loginMethod === 'otp' 
                ? 'bg-white/10 text-white' 
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Phone & OTP
          </button>
        </div>
        
        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-500 text-sm">
            {error}
          </div>
        )}
        
        {loginMethod === 'password' ? (
          <form className="space-y-4" onSubmit={handlePasswordLogin}>
            <div>
              <label className="block text-gray-300 mb-2 text-sm">Email</label>
              <input 
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50" 
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-gray-300 text-sm">Password</label>
                <Link to="/auth/forgot-password" className="text-secondary text-xs hover:underline">
                  Forgot Password?
                </Link>
              </div>
              <input 
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50" 
                placeholder="********"
                required
              />
            </div>
            <button 
              type="submit"
              className="w-full btn-primary py-2 rounded-lg font-medium transition-all flex justify-center items-center"
              disabled={loading}
            >
              {loading ? (
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                'Log In'
              )}
            </button>
          </form>
        ) : (
          <div>
            {!otpSent ? (
              <form className="space-y-4" onSubmit={handleSendOtp}>
                <div>
                  <label className="block text-gray-300 mb-2 text-sm">Phone Number</label>
                  <input 
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50" 
                    placeholder="+1234567890"
                    required
                  />
                </div>
                <button 
                  type="submit"
                  className="w-full btn-primary py-2 rounded-lg font-medium transition-all flex justify-center items-center"
                  disabled={loading}
                >
                  {loading ? (
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    'Send OTP'
                  )}
                </button>
              </form>
            ) : (
              <form className="space-y-4" onSubmit={handleVerifyOtp}>
                <div>
                  <label className="block text-gray-300 mb-2 text-sm">Enter OTP sent to {phoneNumber}</label>
                  <div className="flex space-x-2">
                    <input 
                      type="text"
                      value={otp}
                      onChange={(e) => setOtp(e.target.value)}
                      className="w-full px-4 py-2 rounded-lg bg-white/5 border border-white/10 text-white focus:outline-none focus:ring-2 focus:ring-secondary/50 focus:border-secondary/50 text-center tracking-widest" 
                      placeholder="● ● ● ● ● ●"
                      maxLength={6}
                      required
                    />
                  </div>
                  <div className="text-center mt-3">
                    <button 
                      type="button" 
                      onClick={resendOtp}
                      className="text-secondary text-xs hover:underline"
                      disabled={loading}
                    >
                      Didn't receive code? Resend
                    </button>
                  </div>
                </div>
                <button 
                  type="submit"
                  className="w-full btn-primary py-2 rounded-lg font-medium transition-all flex justify-center items-center"
                  disabled={loading}
                >
                  {loading ? (
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    'Verify & Log In'
                  )}
                </button>
                <button 
                  type="button"
                  onClick={() => setOtpSent(false)}
                  className="w-full py-2 rounded-lg font-medium border border-gray-500 text-gray-300 hover:bg-white/5 transition-all mt-2"
                >
                  Back
                </button>
              </form>
            )}
          </div>
        )}
        
        <div className="mt-8 text-center">
          <p className="text-gray-400">Don't have an account? <Link to="/auth/register" className="text-secondary hover:underline">Register</Link></p>
        </div>
        
      </div>
    </div>
  )
}
