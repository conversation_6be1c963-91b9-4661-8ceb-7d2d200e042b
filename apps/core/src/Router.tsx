import React, { lazy, Suspense, ReactNode } from 'react'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import Layout from './components/layout/Layout'

// Lazy load pages for better performance
const LandingPage = lazy(() => import('./pages/LandingPage'))
const CropMarketplace = lazy(() => import('./pages/CropMarketplace/CropMarketplace'))
const CropDetails = lazy(() => import('./pages/CropMarketplace/CropDetails'))
const CartPage = lazy(() => import('./pages/CartPage'))
const CheckoutPage = lazy(() => import('./pages/CheckoutPage'))
const ProfilePage = lazy(() => import('./pages/ProfilePage'))
const OrderHistory = lazy(() => import('./pages/OrderHistory'))
const PostsPage = lazy(() => import('./pages/PostsPage'))
const PostDetail = lazy(() => import('./pages/PostDetail'))
const Login = lazy(() => import('./pages/AuthPages/Login'))
const Register = lazy(() => import('./pages/AuthPages/Register'))

// Loading component
const LoadingFallback = () => (
  <div className="flex items-center justify-center h-screen bg-bg-default">
    <div className="text-center">
      <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-white">Loading...</p>
    </div>
  </div>
)

// Auth route wrapper component (can be enhanced later for actual auth)
const AuthRoute = ({ children }: { children: ReactNode }) => {
  // Placeholder for authentication logic
  const isAuthenticated = true // Replace with actual auth check
  
  if (!isAuthenticated) {
    return <Navigate to="/auth/login" replace />
  }
  
  return <>{children}</>
}

// Home route that redirects based on auth status
const HomeRoute = () => {
  // Placeholder for authentication logic
  const isAuthenticated = false // Replace with actual auth check
  
  if (isAuthenticated) {
    return <Navigate to="/crops" replace />
  }
  
  return <Layout><LandingPage /></Layout>
}

export default function Router() {
  return (
    <BrowserRouter>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          {/* Auth pages - no layout */}
          <Route path="/auth">
            <Route path="login" element={<Login />} />
            <Route path="register" element={<Register />} />
          </Route>
          
          {/* Main application routes with layout */}
          <Route path="/" element={<HomeRoute />} />
          
          {/* Crops routes */}
          <Route path="/crops" element={<Layout><CropMarketplace /></Layout>} />
          <Route path="/crops/:cropId" element={<Layout><CropDetails /></Layout>} />
          
          {/* User routes - protected */}
          <Route path="/cart" element={<Layout><AuthRoute><CartPage /></AuthRoute></Layout>} />
          <Route path="/checkout" element={<Layout><AuthRoute><CheckoutPage /></AuthRoute></Layout>} />
          <Route path="/profile" element={<Layout><AuthRoute><ProfilePage /></AuthRoute></Layout>} />
          <Route path="/orders" element={<Layout><AuthRoute><OrderHistory /></AuthRoute></Layout>} />
          
          {/* Content routes */}
          <Route path="/posts" element={<Layout><PostsPage /></Layout>} />
          <Route path="/posts/:postId" element={<Layout><PostDetail /></Layout>} />
          
          {/* Fallback route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>
    </BrowserRouter>
  )
}