import { Express } from 'express';
import express from 'express';
import cors from 'cors';
import { config } from '../config';

export const setupMiddleware = (app: Express): void => {
  // Basic middleware
  app.use(cors({
    origin: config.corsOptions.origin,
    credentials: config.corsOptions.credentials
  }));
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
};

export * from './auth.middleware';