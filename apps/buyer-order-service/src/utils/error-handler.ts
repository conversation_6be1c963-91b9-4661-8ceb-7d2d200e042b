import { logger } from './logger';

/**
 * Error types for better categorization
 */
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  PAYMENT_ERROR = 'PAYMENT_ERROR',
  INVENTORY_ERROR = 'INVENTORY_ERROR',
  FULFILLMENT_ERROR = 'FULFILLMENT_ERROR',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  BUSINESS_RULE_ERROR = 'BUSINESS_RULE_ERROR',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'
}

/**
 * Error context interface
 */
export interface ErrorContext {
  correlationId?: string;
  userId?: string;
  orderId?: string;
  operation?: string;
  sellerId?: string;
  paymentId?: string;
  originalError?: Error;
  [key: string]: any;
}

/**
 * Enhanced AppError class with comprehensive error handling
 */
export class AppError extends Error {
  statusCode: number;
  status: string;
  type: ErrorType;
  code: string;
  context: ErrorContext;
  isOperational: boolean;
  timestamp: Date;

  constructor(
    statusCode: number,
    message: string,
    type: ErrorType = ErrorType.INTERNAL_SERVER_ERROR,
    context: ErrorContext = {},
    isOperational: boolean = true
  ) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.type = type;
    this.code = type; // Use type as code
    this.context = context;
    this.isOperational = isOperational;
    this.timestamp = new Date();
    this.name = 'AppError';

    // Ensure the prototype chain is correct
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this, this.constructor);

    // Log the error
    logger.error(`AppError: ${message}`, this, {
      statusCode,
      type,
      ...context
    });
  }
}

/**
 * Specialized error classes
 */
export class ValidationError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(400, message, ErrorType.VALIDATION_ERROR, context);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(404, message, ErrorType.NOT_FOUND_ERROR, context);
    this.name = 'NotFoundError';
  }
}

export class PaymentError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(402, message, ErrorType.PAYMENT_ERROR, context);
    this.name = 'PaymentError';
  }
}

export class InventoryError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(409, message, ErrorType.INVENTORY_ERROR, context);
    this.name = 'InventoryError';
  }
}

export class FulfillmentError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(422, message, ErrorType.FULFILLMENT_ERROR, context);
    this.name = 'FulfillmentError';
  }
}

export class BusinessRuleError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(422, message, ErrorType.BUSINESS_RULE_ERROR, context);
    this.name = 'BusinessRuleError';
  }
}

export class ExternalApiError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(503, message, ErrorType.EXTERNAL_API_ERROR, context);
    this.name = 'ExternalApiError';
  }
}

/**
 * Error handling utilities
 */
export function isOperationalError(error: Error): boolean {
  if (error instanceof AppError) {
    return error.isOperational;
  }
  return false;
}

export function handlePaymentError(error: any, operation: string, context: ErrorContext = {}): PaymentError {
  const message = `Payment ${operation} failed: ${error.message || error}`;
  return new PaymentError(message, {
    ...context,
    operation: `payment.${operation}`,
    originalError: error
  });
}

export function handleInventoryError(error: any, operation: string, context: ErrorContext = {}): InventoryError {
  const message = `Inventory ${operation} failed: ${error.message || error}`;
  return new InventoryError(message, {
    ...context,
    operation: `inventory.${operation}`,
    originalError: error
  });
}

export function handleFulfillmentError(error: any, operation: string, context: ErrorContext = {}): FulfillmentError {
  const message = `Fulfillment ${operation} failed: ${error.message || error}`;
  return new FulfillmentError(message, {
    ...context,
    operation: `fulfillment.${operation}`,
    originalError: error
  });
}

export function handleExternalApiError(error: any, service: string, context: ErrorContext = {}): ExternalApiError {
  const message = `External API call to ${service} failed: ${error.message || error}`;
  return new ExternalApiError(message, {
    ...context,
    operation: `external.${service}`,
    originalError: error
  });
}

/**
 * Async error wrapper for better error handling
 */
export function asyncErrorHandler<T extends any[], R>(
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }

      // Convert unknown errors to AppError
      const message = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new AppError(500, message, ErrorType.INTERNAL_SERVER_ERROR, {
        originalError: error instanceof Error ? error : new Error(String(error))
      });
    }
  };
}

/**
 * Retry mechanism for operations that might fail temporarily
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  operationName: string = 'operation'
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.debug(`Attempting ${operationName} (attempt ${attempt}/${maxRetries})`);
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt === maxRetries) {
        logger.error(`${operationName} failed after ${maxRetries} attempts`, lastError);
        break;
      }

      logger.warn(`${operationName} failed on attempt ${attempt}, retrying in ${delay}ms`, {
        error: lastError.message,
        attempt,
        maxRetries
      });

      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // Exponential backoff
    }
  }

  throw lastError!;
}

/**
 * Enhanced global error handling function
 */
export const handleError = (err: unknown, context: ErrorContext = {}) => {
  logger.error('Global error handler triggered', err, context);

  if (err instanceof AppError) {
    return {
      statusCode: err.statusCode,
      status: err.status,
      type: err.type,
      message: err.message,
      timestamp: err.timestamp,
      correlationId: context.correlationId,
      ...(process.env.NODE_ENV === 'development' && {
        stack: err.stack,
        context: err.context
      })
    };
  }

  // Handle Mongoose validation errors
  if (err && typeof err === 'object' && 'name' in err && err.name === 'ValidationError') {
    return {
      statusCode: 400,
      status: 'fail',
      type: ErrorType.VALIDATION_ERROR,
      message: 'Validation failed',
      timestamp: new Date(),
      correlationId: context.correlationId
    };
  }

  // Handle Mongoose cast errors
  if (err && typeof err === 'object' && 'name' in err && err.name === 'CastError') {
    return {
      statusCode: 400,
      status: 'fail',
      type: ErrorType.VALIDATION_ERROR,
      message: 'Invalid data format',
      timestamp: new Date(),
      correlationId: context.correlationId
    };
  }

  // Handle JWT errors
  if (err && typeof err === 'object' && 'name' in err &&
      ['JsonWebTokenError', 'TokenExpiredError'].includes(err.name as string)) {
    return {
      statusCode: 401,
      status: 'fail',
      type: ErrorType.AUTHENTICATION_ERROR,
      message: 'Authentication failed',
      timestamp: new Date(),
      correlationId: context.correlationId
    };
  }

  // Default to 500 Internal Server Error
  return {
    statusCode: 500,
    status: 'error',
    type: ErrorType.INTERNAL_SERVER_ERROR,
    message: process.env.NODE_ENV === 'production' ? 'Internal Server Error' :
             (err instanceof Error ? err.message : String(err)),
    timestamp: new Date(),
    correlationId: context.correlationId
  };
};