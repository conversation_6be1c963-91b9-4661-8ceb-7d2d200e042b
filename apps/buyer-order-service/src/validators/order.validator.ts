import { z } from 'zod';

// Schema for individual order items
const OrderItemSchema = z.object({
  productId: z.string().min(1, 'Product ID cannot be empty'), // Assuming string IDs for now
  quantity: z.number().int().positive('Quantity must be a positive integer'),
  price: z.number().nonnegative('Price cannot be negative'),
  unitPrice: z.number().nonnegative('Unit price cannot be negative'),
});

// Schema for the createOrder request body (agricultural marketplace - no shipping needed)
export const createOrderSchema = z.object({
  items: z.array(OrderItemSchema).min(1, 'Order must contain at least one item'),
  paymentMethod: z.string().optional(),
  buyerNotes: z.string().optional(),
  seasonalInfo: z.object({
    season: z.string().optional(),
    harvestDate: z.string().optional()
  }).optional(),
  qualityAssurance: z.object({
    requirements: z.array(z.string()).optional(),
    certifications: z.array(z.string()).optional()
  }).optional()
});

// Schema for updating order (agricultural marketplace - no delivery/shipping)
export const updateOrderSchema = z.object({
  paymentMethod: z.string().optional(),
  buyerNotes: z.string().optional(),
  seasonalInfo: z.object({
    season: z.string().optional(),
    harvestDate: z.string().optional()
  }).optional(),
  qualityAssurance: z.object({
    requirements: z.array(z.string()).optional(),
    certifications: z.array(z.string()).optional()
  }).optional()
});

// Schema for order query options
export const orderQuerySchema = z.object({
  status: z.string().optional(),
  userId: z.string().optional(),
  sellerId: z.string().optional(),
  page: z.number().int().positive().optional(),
  limit: z.number().int().positive().max(100).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
});

// Type inferred from the schemas
export type CreateOrderInput = z.infer<typeof createOrderSchema>;
export type UpdateOrderInput = z.infer<typeof updateOrderSchema>;
export type OrderQueryOptions = z.infer<typeof orderQuerySchema>;