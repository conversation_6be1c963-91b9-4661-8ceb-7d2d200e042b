import dotenv from 'dotenv';
dotenv.config(); // Load .env file

// Configuration structure matching main.ts expectations
export const config = {
  env: (process.env.NODE_ENV as 'development' | 'production' | 'test') || 'development',
  port: parseInt(process.env.ORDER_SERVICE_PORT || '6003', 10),
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/buyer_backend'
  },
  corsOptions: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true as const
  },
  kafka: {
    brokers: (process.env.KAFKA_BROKERS || 'localhost:9092').split(','),
    clientId: process.env.KAFKA_CLIENT_ID || 'buyer-order-service',
    topics: {
        cartCheckedOut: process.env.KAFKA_TOPIC_CART_CHECKED_OUT || 'cart.checkedout',
        paymentProcessed: process.env.KAFKA_TOPIC_PAYMENT_PROCESSED || 'payment.processed',
        paymentFailed: process.env.KAFKA_TOPIC_PAYMENT_FAILED || 'payment.failed',
        orderCreated: process.env.KAFKA_TOPIC_ORDER_CREATED || 'order.created',
        orderPaid: process.env.KAFKA_TOPIC_ORDER_PAID || 'order.paid',
        orderCancelled: process.env.KAFKA_TOPIC_ORDER_CANCELLED || 'order.cancelled',
        orderShipped: process.env.KAFKA_TOPIC_ORDER_SHIPPED || 'order.shipped',
        orderDelivered: process.env.KAFKA_TOPIC_ORDER_DELIVERED || 'order.delivered',
    },
    groupId: process.env.KAFKA_GROUP_ID_ORDER || 'order-service-group'
  }
} as const;