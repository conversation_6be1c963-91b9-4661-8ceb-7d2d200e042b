import mongoose, { Schema, Document, Types } from 'mongoose';

/**
 * Product types for agricultural orders
 */
export enum ProductType {
  PLOT = 'PLOT',
  CROP = 'CROP',
  FARM_SERVICE = 'FARM_SERVICE',
  EQUIPMENT = 'EQUIPMENT',
  SUPPLY = 'SUPPLY'
}

/**
 * Order status enum with agricultural-specific statuses
 */
export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PAID = 'PAID',
  PROCESSING = 'PROCESSING',
  HARVESTING = 'HARVESTING',
  QUALITY_CHECK = 'QUALITY_CHECK',
  PACKAGING = 'PACKAGING',
  SHIPPED = 'SHIPPED',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  DISPUTED = 'DISPUTED'
}

/**
 * Payment status enum
 */
export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED'
}

/**
 * Delivery method enum
 */
export enum DeliveryMethod {
  PICKUP = 'PICKUP',
  STANDARD_DELIVERY = 'STANDARD_DELIVERY',
  EXPRESS_DELIVERY = 'EXPRESS_DELIVERY',
  COLD_CHAIN = 'COLD_CHAIN',
  BULK_TRANSPORT = 'BULK_TRANSPORT'
}

/**
 * Quality requirements interface
 */
export interface QualityRequirements {
  grade?: string;
  specifications?: string[];
  certifications?: string[];
  inspectionRequired?: boolean;
  qualityStandards?: string[];
}

/**
 * Delivery schedule interface
 */
export interface DeliverySchedule {
  preferredDate?: Date;
  preferredTimeSlot?: string;
  deliveryWindow?: {
    startDate: Date;
    endDate: Date;
  };
  isFlexible?: boolean;
  specialInstructions?: string;
}

/**
 * Agricultural-specific order item interface
 */
export interface IOrderItem {
  productId: Types.ObjectId;
  sellerId: Types.ObjectId;
  productType: ProductType;

  // Product snapshot at time of order
  productSnapshot: {
    name: string;
    description: string;
    category: string;
    subCategory?: string;
    variety?: string;
    farmingMethod?: string;
    images?: string[];
  };

  // Pricing and quantity
  unitPrice: number;
  quantity: number;
  unit: string; // kg, tons, acres, pieces, etc.
  totalPrice: number;

  // Agricultural-specific fields
  qualityRequirements?: QualityRequirements;
  harvestDate?: Date;
  expiryDate?: Date;
  storageRequirements?: string;

  // For crop ownership/investment
  ownershipPercentage?: number;
  expectedYield?: number;
  yieldUnit?: string;

  // Service-specific fields (for FARM_SERVICE)
  serviceDate?: Date;
  serviceDuration?: string;
  serviceArea?: string;

  // Equipment rental fields
  rentalPeriod?: {
    startDate: Date;
    endDate: Date;
  };

  // Status tracking
  itemStatus: OrderStatus;
  statusHistory: {
    status: OrderStatus;
    timestamp: Date;
    notes?: string;
    updatedBy?: string;
  }[];
}

/**
 * Shipping address interface
 */
export interface ShippingAddress {
  recipientName: string;
  recipientPhone: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  landmark?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  addressType?: 'HOME' | 'OFFICE' | 'FARM' | 'WAREHOUSE';
}

/**
 * Payment details interface
 */
export interface PaymentDetails {
  paymentId?: string;
  method: string;
  status: PaymentStatus;
  amount: number;
  currency: string;
  transactionId?: string;
  gatewayResponse?: any;

  // For installment payments
  installments?: {
    totalInstallments: number;
    paidInstallments: number;
    nextDueDate?: Date;
    installmentAmount: number;
  };

  // For escrow payments (high-value transactions)
  escrowDetails?: {
    escrowId: string;
    releaseConditions: string[];
    isReleased: boolean;
    releasedAt?: Date;
  };
}

/**
 * Comprehensive Order document interface
 */
export interface IOrder extends Document {
  // Basic order information
  orderNumber: string;
  userId: Types.ObjectId;
  items: IOrderItem[];

  // Pricing (no shipping charges for agricultural marketplace)
  subtotal: number;
  taxes: number;
  discounts: number;
  totalAmount: number;
  currency: string;

  // Status and tracking
  status: OrderStatus;
  statusHistory: {
    status: OrderStatus;
    timestamp: Date;
    notes?: string;
    updatedBy?: string;
  }[];

  // No delivery/shipping for agricultural marketplace

  // Payment information
  paymentDetails: PaymentDetails;

  // Agricultural-specific fields
  seasonalInfo?: {
    season: string;
    harvestPeriod?: string;
    plantingDate?: Date;
  };

  // Quality and compliance
  qualityAssurance?: {
    inspectionRequired: boolean;
    inspectionDate?: Date;
    inspectionReport?: string;
    qualityGrade?: string;
    certifications?: string[];
  };

  // Communication and notes
  buyerNotes?: string;
  sellerNotes?: string;
  internalNotes?: string;

  // Timestamps and metadata
  estimatedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  completedAt?: Date;
  cancelledAt?: Date;
  cancellationReason?: string;

  // Ratings and feedback
  buyerRating?: {
    rating: number;
    review?: string;
    ratedAt: Date;
  };

  sellerRating?: {
    rating: number;
    review?: string;
    ratedAt: Date;
  };

  createdAt: Date;
  updatedAt: Date;

  // Method declarations
  isPaid(): boolean;
  addStatusUpdate(status: OrderStatus, notes?: string, updatedBy?: string): void;
  canBeCancelled(): boolean;
  canBeModified(): boolean;
  isDelivered(): boolean;
}

// Quality requirements schema
const QualityRequirementsSchema = new Schema({
  grade: { type: String },
  specifications: [{ type: String }],
  certifications: [{ type: String }],
  inspectionRequired: { type: Boolean, default: false },
  qualityStandards: [{ type: String }]
}, { _id: false });

// Delivery schedule schema
const DeliveryScheduleSchema = new Schema({
  preferredDate: { type: Date },
  preferredTimeSlot: { type: String },
  deliveryWindow: {
    startDate: { type: Date },
    endDate: { type: Date }
  },
  isFlexible: { type: Boolean, default: true },
  specialInstructions: { type: String }
}, { _id: false });

// Status history schema
const StatusHistorySchema = new Schema({
  status: {
    type: String,
    enum: Object.values(OrderStatus),
    required: true
  },
  timestamp: { type: Date, default: Date.now },
  notes: { type: String },
  updatedBy: { type: String }
}, { _id: false });

// Enhanced order item schema
const OrderItemSchema: Schema = new Schema({
  productId: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
  },
  sellerId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  productType: {
    type: String,
    enum: Object.values(ProductType),
    required: true
  },

  // Product snapshot
  productSnapshot: {
    name: { type: String, required: true },
    description: { type: String, required: true },
    category: { type: String, required: true },
    subCategory: { type: String },
    variety: { type: String },
    farmingMethod: { type: String },
    images: [{ type: String }]
  },

  // Pricing and quantity
  unitPrice: { type: Number, required: true, min: 0 },
  quantity: { type: Number, required: true, min: 0 },
  unit: { type: String, required: true },
  totalPrice: { type: Number, required: true, min: 0 },

  // Agricultural-specific fields
  qualityRequirements: QualityRequirementsSchema,
  harvestDate: { type: Date },
  expiryDate: { type: Date },
  storageRequirements: { type: String },

  // For crop ownership/investment
  ownershipPercentage: { type: Number, min: 0, max: 100 },
  expectedYield: { type: Number, min: 0 },
  yieldUnit: { type: String },

  // Service-specific fields
  serviceDate: { type: Date },
  serviceDuration: { type: String },
  serviceArea: { type: String },

  // Equipment rental fields
  rentalPeriod: {
    startDate: { type: Date },
    endDate: { type: Date }
  },

  // Status tracking
  itemStatus: {
    type: String,
    enum: Object.values(OrderStatus),
    default: OrderStatus.PENDING
  },
  statusHistory: [StatusHistorySchema]
}, { _id: false });

// Shipping address schema
const ShippingAddressSchema = new Schema({
  recipientName: { type: String, required: true },
  recipientPhone: { type: String, required: true },
  addressLine1: { type: String, required: true },
  addressLine2: { type: String },
  city: { type: String, required: true },
  state: { type: String, required: true },
  postalCode: { type: String, required: true },
  country: { type: String, required: true, default: 'India' },
  landmark: { type: String },
  coordinates: {
    latitude: { type: Number },
    longitude: { type: Number }
  },
  addressType: {
    type: String,
    enum: ['HOME', 'OFFICE', 'FARM', 'WAREHOUSE'],
    default: 'HOME'
  }
}, { _id: false });

// Payment details schema
const PaymentDetailsSchema = new Schema({
  paymentId: { type: String },
  method: { type: String, required: true },
  status: {
    type: String,
    enum: Object.values(PaymentStatus),
    default: PaymentStatus.PENDING
  },
  amount: { type: Number, required: true, min: 0 },
  currency: { type: String, default: 'INR' },
  transactionId: { type: String },
  gatewayResponse: { type: Schema.Types.Mixed },

  // Installment payments
  installments: {
    totalInstallments: { type: Number, min: 1 },
    paidInstallments: { type: Number, default: 0 },
    nextDueDate: { type: Date },
    installmentAmount: { type: Number, min: 0 }
  },

  // Escrow payments
  escrowDetails: {
    escrowId: { type: String },
    releaseConditions: [{ type: String }],
    isReleased: { type: Boolean, default: false },
    releasedAt: { type: Date }
  }
}, { _id: false });

// Rating schema
const RatingSchema = new Schema({
  rating: { type: Number, min: 1, max: 5, required: true },
  review: { type: String },
  ratedAt: { type: Date, default: Date.now }
}, { _id: false });

// Enhanced order schema
const OrderSchema: Schema = new Schema(
  {
    // Basic order information
    orderNumber: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    items: {
      type: [OrderItemSchema],
      required: true,
      validate: {
        validator: function(items: any[]) {
          return items && items.length > 0;
        },
        message: 'Order must have at least one item'
      }
    },

    // Pricing (no shipping charges for agricultural marketplace)
    subtotal: { type: Number, required: true, min: 0 },
    taxes: { type: Number, default: 0, min: 0 },
    discounts: { type: Number, default: 0, min: 0 },
    totalAmount: { type: Number, required: true, min: 0 },
    currency: { type: String, default: 'INR' },

    // Status and tracking
    status: {
      type: String,
      enum: Object.values(OrderStatus),
      default: OrderStatus.PENDING,
      required: true,
      index: true,
    },
    statusHistory: [StatusHistorySchema],

    // No delivery/shipping for agricultural marketplace

    // Payment information
    paymentDetails: { type: PaymentDetailsSchema, required: true },

    // Agricultural-specific fields
    seasonalInfo: {
      season: { type: String },
      harvestPeriod: { type: String },
      plantingDate: { type: Date }
    },

    // Quality and compliance
    qualityAssurance: {
      inspectionRequired: { type: Boolean, default: false },
      inspectionDate: { type: Date },
      inspectionReport: { type: String },
      qualityGrade: { type: String },
      certifications: [{ type: String }]
    },

    // Communication and notes
    buyerNotes: { type: String },
    sellerNotes: { type: String },
    internalNotes: { type: String },

    // Timestamps and metadata
    estimatedDeliveryDate: { type: Date },
    actualDeliveryDate: { type: Date },
    completedAt: { type: Date },
    cancelledAt: { type: Date },
    cancellationReason: { type: String },

    // Ratings and feedback
    buyerRating: RatingSchema,
    sellerRating: RatingSchema
  },
  {
    timestamps: true,
    collection: 'orders',
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for better query performance
OrderSchema.index({ userId: 1, status: 1 });
OrderSchema.index({ orderNumber: 1 });
OrderSchema.index({ 'items.productId': 1 });
OrderSchema.index({ 'items.sellerId': 1 });
OrderSchema.index({ createdAt: -1 });
OrderSchema.index({ estimatedDeliveryDate: 1 });
OrderSchema.index({ 'paymentDetails.status': 1 });

// Virtual for order age
OrderSchema.virtual('orderAge').get(function(this: IOrder) {
  return Date.now() - this.createdAt.getTime();
});

// Virtual for days until delivery
OrderSchema.virtual('daysUntilDelivery').get(function(this: IOrder) {
  if (!this.estimatedDeliveryDate) return null;
  const now = new Date();
  const delivery = new Date(this.estimatedDeliveryDate);
  const diffTime = delivery.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual for order summary
OrderSchema.virtual('summary').get(function(this: IOrder) {
  return {
    orderNumber: this.orderNumber,
    itemCount: this.items.length,
    totalAmount: this.totalAmount,
    status: this.status,
    createdAt: this.createdAt
  };
});

// Pre-save middleware to generate order number
OrderSchema.pre('save', async function(this: IOrder, next) {
  if (this.isNew && !this.orderNumber) {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    this.orderNumber = `AGR-${timestamp.slice(-8)}-${random}`;
  }

  // Update status history
  if (this.isModified('status')) {
    this.statusHistory.push({
      status: this.status,
      timestamp: new Date(),
      notes: `Status changed to ${this.status}`
    });
  }

  next();
});

// Instance methods
OrderSchema.methods.canBeCancelled = function(this: IOrder): boolean {
  const cancellableStatuses = [
    OrderStatus.PENDING,
    OrderStatus.CONFIRMED,
    OrderStatus.PAID
  ];
  return cancellableStatuses.includes(this.status);
};

OrderSchema.methods.canBeModified = function(this: IOrder): boolean {
  const modifiableStatuses = [
    OrderStatus.PENDING,
    OrderStatus.CONFIRMED
  ];
  return modifiableStatuses.includes(this.status);
};

OrderSchema.methods.isDelivered = function(this: IOrder): boolean {
  return this.status === OrderStatus.DELIVERED || this.status === OrderStatus.COMPLETED;
};

OrderSchema.methods.isPaid = function(this: IOrder): boolean {
  return this.paymentDetails.status === PaymentStatus.COMPLETED;
};

OrderSchema.methods.addStatusUpdate = function(this: IOrder, status: OrderStatus, notes?: string, updatedBy?: string) {
  this.status = status;
  this.statusHistory.push({
    status,
    timestamp: new Date(),
    notes,
    updatedBy
  });
};

OrderSchema.methods.canBeCancelled = function(this: IOrder): boolean {
  return [OrderStatus.PENDING, OrderStatus.CONFIRMED, OrderStatus.PROCESSING].includes(this.status);
};

OrderSchema.methods.canBeModified = function(this: IOrder): boolean {
  return [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(this.status);
};

OrderSchema.methods.isDelivered = function(this: IOrder): boolean {
  return this.status === OrderStatus.DELIVERED;
};

// Static methods
OrderSchema.statics.findByOrderNumber = function(orderNumber: string) {
  return this.findOne({ orderNumber });
};

OrderSchema.statics.findByUser = function(userId: string, options: any = {}) {
  const query = this.find({ userId });

  if (options.status) {
    query.where('status').equals(options.status);
  }

  if (options.limit) {
    query.limit(options.limit);
  }

  if (options.sort) {
    query.sort(options.sort);
  } else {
    query.sort({ createdAt: -1 });
  }

  return query;
};

OrderSchema.statics.findBySeller = function(sellerId: string, options: any = {}) {
  const query = this.find({ 'items.sellerId': sellerId });

  if (options.status) {
    query.where('status').equals(options.status);
  }

  if (options.limit) {
    query.limit(options.limit);
  }

  if (options.sort) {
    query.sort(options.sort);
  } else {
    query.sort({ createdAt: -1 });
  }

  return query;
};

OrderSchema.statics.getOrderStats = function(userId?: string) {
  const matchStage = userId ? { $match: { userId: new mongoose.Types.ObjectId(userId) } } : { $match: {} };

  return this.aggregate([
    matchStage,
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$totalAmount' }
      }
    }
  ]);
};

// Export interfaces and types (enums are already exported above)
// Note: These types are already exported individually above, so we don't need to re-export them

const OrderModel = mongoose.model<IOrder>('Order', OrderSchema);

export default OrderModel;