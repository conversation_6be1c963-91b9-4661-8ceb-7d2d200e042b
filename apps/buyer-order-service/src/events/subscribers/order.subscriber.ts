// Placeholder for Kafka Consumer setup and event subscription logic
import { Kafka, EachMessagePayload } from 'kafkajs';
import { config } from '../../config/config';
import { handleCartCheckedOut, handlePaymentProcessed, handlePaymentFailed } from '../handlers/order.handler';

// Initialize Kafka consumer
const kafka = new Kafka({
  clientId: config.kafka.clientId,
  brokers: config.kafka.brokers,
});
const consumer = kafka.consumer({ groupId: config.kafka.groupId });

let isConsumerConnected = false;

export const connectConsumer = async () => {
  if (isConsumerConnected) return;
  try {
    await consumer.connect();
    isConsumerConnected = true;
    console.log('Order Service Kafka Consumer connected');

    // Subscribe to relevant topics
    await consumer.subscribe({ topic: config.kafka.topics.cartCheckedOut, fromBeginning: true });
    await consumer.subscribe({ topic: config.kafka.topics.paymentProcessed, fromBeginning: true });
    await consumer.subscribe({ topic: config.kafka.topics.paymentFailed, fromBeginning: true });

    await consumer.run({
      eachMessage: async ({ topic, partition, message }: EachMessagePayload) => {
        const messageValueString = message.value?.toString();
        console.log(`Received message from topic ${topic}:`, {
          partition,
          offset: message.offset,
          // Avoid logging potentially large message value here, log in handler if needed
          // value: messageValueString,
        });

        if (!message.value || !messageValueString) {
          console.warn(`Received empty or invalid message from topic ${topic}, offset ${message.offset}`);
          // Acknowledge the message? Depending on KafkaJS config, commit offsets manually?
          return; 
        }

        let eventData: any;
        try {
          eventData = JSON.parse(messageValueString);
        } catch (parseError) {
          console.error(`Failed to parse JSON message from topic ${topic}, offset ${message.offset}:`, parseError);
          // TODO: Send to Dead Letter Queue (DLQ) or log for investigation
          // await sendToDLQ(topic, messageValueString, parseError);
          return; // Stop processing this message
        }

        try {
          // Route event to the appropriate handler based on topic
          switch (topic) {
            case config.kafka.topics.cartCheckedOut:
              await handleCartCheckedOut(eventData);
              break;
            case config.kafka.topics.paymentProcessed:
              await handlePaymentProcessed(eventData); 
              break;
            case config.kafka.topics.paymentFailed:
              await handlePaymentFailed(eventData); 
              break;
            default:
              console.warn(`Received message from unhandled topic: ${topic}`);
              // Acknowledge or ignore?
          }
          console.log(`Successfully processed message from topic ${topic}, offset ${message.offset}`);
        } catch (processingError) {
          console.error(`Error processing message from topic ${topic}, offset ${message.offset}:`, processingError);
          // TODO: Implement retry logic with backoff or send to DLQ
          // await sendToDLQ(topic, eventData, processingError);
          // Consider not automatically committing offset if retries are needed
        }
      },
    });

  } catch (error) {
      console.error('Failed to connect or subscribe Kafka Consumer:', error);
      // process.exit(1); // Optional: Exit if consumer cannot connect
  }
};

export const disconnectConsumer = async () => {
  if (!isConsumerConnected) return;
  try {
      await consumer.disconnect();
      isConsumerConnected = false;
      console.log('Order Service Kafka Consumer disconnected');
  } catch (error) {
      console.error('Failed to disconnect Kafka Consumer gracefully:', error);
  }
};

// Ensure consumer is connected after app starts and disconnected gracefully 