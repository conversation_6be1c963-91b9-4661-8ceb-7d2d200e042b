// Placeholder for Kafka Producer setup and event publishing functions
import { Kafka } from 'kafkajs';
import { config } from '../../config/config';
import { IOrder } from '../../models/order.model';

// Initialize Kafka producer (consider moving to a shared client setup)
const kafka = new Kafka({
  clientId: config.kafka.clientId,
  brokers: config.kafka.brokers,
});
const producer = kafka.producer();

let isProducerConnected = false;

export const connectProducer = async () => {
  if (isProducerConnected) return;
  try {
    await producer.connect();
    isProducerConnected = true;
    console.log('Order Service Kafka Producer connected');
  } catch (error) {
    console.error('Failed to connect Kafka Producer:', error);
    // Optional: Implement retry logic or exit process based on strategy
    // process.exit(1); 
  }
};

export const disconnectProducer = async () => {
  if (!isProducerConnected) return;
  try {
    await producer.disconnect();
    isProducerConnected = false;
    console.log('Order Service Kafka Producer disconnected');
  } catch (error) {
    console.error('Failed to disconnect Kafka Producer gracefully:', error);
  }
};

// Generic event publisher function (can be specialized)
const publishEvent = async (topic: string, message: any) => {
  if (!isProducerConnected) {
    console.error('Kafka Producer is not connected. Cannot publish event.', { topic, message });
    // Depending on criticality, could throw an error to signal failure upstream
    // throw new Error('Kafka Producer not connected');
    return; 
  }
  try {
    await producer.send({
      topic: topic,
      messages: [{ value: JSON.stringify(message) }],
    });
    console.log(`Event published successfully to topic ${topic}`);
  } catch (error) {
    console.error(`Error publishing event to topic ${topic}:`, error);
    // TODO: Implement more robust error handling:
    // - Retry logic with backoff
    // - Dead-letter queue for failed messages
    // - Monitoring/Alerting
  }
};

// Specific event publishing functions
export const publishOrderCreatedEvent = async (order: IOrder) => {
  // Convert Mongoose doc to plain object if needed
  await publishEvent(config.kafka.topics.orderCreated, order.toObject ? order.toObject() : order);
};

export const publishOrderPaidEvent = async (order: IOrder) => {
   await publishEvent(config.kafka.topics.orderPaid, order.toObject ? order.toObject() : order);
};

export const publishOrderCancelledEvent = async (order: IOrder) => {
   await publishEvent(config.kafka.topics.orderCancelled, order.toObject ? order.toObject() : order);
};

// Add publishers for OrderShipped, OrderDelivered as needed
export const publishOrderShippedEvent = async (order: IOrder) => {
   await publishEvent(config.kafka.topics.orderShipped, order.toObject ? order.toObject() : order);
};

export const publishOrderDeliveredEvent = async (order: IOrder) => {
   await publishEvent(config.kafka.topics.orderDelivered, order.toObject ? order.toObject() : order);
};

export const publishOrderStatusUpdatedEvent = async (order: IOrder) => {
   await publishEvent(config.kafka.topics.orderCreated, order.toObject ? order.toObject() : order);
};

// Ensure producer is connected before publishing and disconnected gracefully on shutdown