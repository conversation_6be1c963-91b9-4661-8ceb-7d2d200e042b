// Placeholder for event handler logic (called by subscriber)
import { SimpleOrderService } from '../../services/simple-order.service';
import {
    PaymentProcessedSchema,
    PaymentFailedSchema,
    CartCheckedOutSchema,
    PaymentProcessedPayload,
    PaymentFailedPayload,
    CartCheckedOutPayload
} from '../schemas/order.schema'; // Import schemas and types

// Instantiate service (consider dependency injection)
const orderService = new SimpleOrderService();

// Remove manual interfaces if relying solely on Zod types
// interface PaymentEventPayload { ... }
// interface CartCheckedOutPayload { ... }

// Handler for PaymentProcessed event
export const handlePaymentProcessed = async (payload: unknown) => {
    console.log('Handling Payment Processed Event - Received Payload:', payload);
    // Validate payload using Zod schema
    const validationResult = PaymentProcessedSchema.safeParse(payload);
    if (!validationResult.success) {
        console.error('Invalid PaymentProcessed payload:', validationResult.error.errors);
        // TODO: Potentially send to DLQ
        return; 
    }
    const validatedPayload: PaymentProcessedPayload = validationResult.data;
    console.log('Validated PaymentProcessed Payload:', validatedPayload);
    
    // Update order status to confirmed/paid
    await orderService.updateOrderStatus(validatedPayload.orderId, 'CONFIRMED' as any);
};

// Handler for PaymentFailed event
export const handlePaymentFailed = async (payload: unknown) => {
    console.log('Handling Payment Failed Event - Received Payload:', payload);
    const validationResult = PaymentFailedSchema.safeParse(payload);
    if (!validationResult.success) {
        console.error('Invalid PaymentFailed payload:', validationResult.error.errors);
        // TODO: Potentially send to DLQ
        return;
    }
    const validatedPayload: PaymentFailedPayload = validationResult.data;
    console.log('Validated PaymentFailed Payload:', validatedPayload);

    // Update order status to failed
    await orderService.updateOrderStatus(validatedPayload.orderId, 'CANCELLED' as any);
};

// Handler for CartCheckedOut event
export const handleCartCheckedOut = async (payload: unknown) => {
    console.log('Handling Cart Checked Out Event - Received Payload:', payload);
    const validationResult = CartCheckedOutSchema.safeParse(payload);
    if (!validationResult.success) {
        console.error('Invalid CartCheckedOut payload:', validationResult.error.errors);
        // TODO: Potentially send to DLQ
        return;
    }
    const validatedPayload: CartCheckedOutPayload = validationResult.data;
    console.log('Validated CartCheckedOut Payload:', validatedPayload);

    // Map CartCheckedOut payload to CreateOrderInput for the service
    // The Zod schema `CartCheckedOutPayload` should align with `CreateOrderInput`
    const orderData = {
        items: validatedPayload.items, 
        shippingAddress: validatedPayload.shippingAddress,
    };

    // The types should now match if schemas are aligned
    const fullOrderData = { ...orderData, userId: validatedPayload.userId };
    await orderService.createOrder(fullOrderData);
}; 