import { IOrder, <PERSON><PERSON>rderItem, OrderStatus } from '../models/order.model';
import { AppError } from '../utils/error-handler';
import axios from 'axios';

/**
 * Notification types
 */
export enum NotificationType {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  PUSH = 'PUSH',
  IN_APP = 'IN_APP'
}

/**
 * Notification template interface
 */
export interface NotificationTemplate {
  id: string;
  type: NotificationType;
  subject?: string;
  title: string;
  body: string;
  variables: string[];
}

/**
 * Notification request interface
 */
export interface NotificationRequest {
  userId: string;
  type: NotificationType;
  templateId: string;
  variables: { [key: string]: any };
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  scheduledAt?: Date;
}

/**
 * Notification service for sending order-related notifications
 */
export class NotificationService {
  private notificationServiceUrl: string;
  private templates: Map<string, NotificationTemplate>;

  constructor() {
    this.notificationServiceUrl = process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005';
    this.templates = new Map();
    this.loadNotificationTemplates();
  }

  /**
   * Send order created notification
   */
  async sendOrderCreatedNotification(order: IOrder): Promise<void> {
    try {
      const notifications: NotificationRequest[] = [
        {
          userId: order.userId.toString(),
          type: NotificationType.EMAIL,
          templateId: 'ORDER_CREATED_EMAIL',
          variables: {
            orderNumber: order.orderNumber,
            totalAmount: order.totalAmount,
            currency: order.currency,
            itemCount: order.items.length,
            estimatedDeliveryDate: order.estimatedDeliveryDate?.toDateString()
          },
          priority: 'MEDIUM'
        },
        {
          userId: order.userId.toString(),
          type: NotificationType.PUSH,
          templateId: 'ORDER_CREATED_PUSH',
          variables: {
            orderNumber: order.orderNumber,
            totalAmount: order.totalAmount
          },
          priority: 'MEDIUM'
        }
      ];

      await this.sendBulkNotifications(notifications);

      // Notify sellers
      const sellerIds = [...new Set(order.items.map(item => item.sellerId.toString()))];
      for (const sellerId of sellerIds) {
        await this.sendSellerOrderNotification(sellerId, order);
      }

    } catch (error) {
      console.error('Failed to send order created notification:', error);
      // Don't throw error as notification failure shouldn't break order creation
    }
  }

  /**
   * Send order status update notification
   */
  async sendOrderStatusUpdateNotification(order: IOrder): Promise<void> {
    try {
      const templateId = this.getStatusUpdateTemplateId(order.status);
      
      const notifications: NotificationRequest[] = [
        {
          userId: order.userId.toString(),
          type: NotificationType.EMAIL,
          templateId: `${templateId}_EMAIL`,
          variables: {
            orderNumber: order.orderNumber,
            status: order.status,
            // No tracking for agricultural marketplace
            estimatedDeliveryDate: order.estimatedDeliveryDate?.toDateString()
          },
          priority: this.getNotificationPriority(order.status)
        },
        {
          userId: order.userId.toString(),
          type: NotificationType.PUSH,
          templateId: `${templateId}_PUSH`,
          variables: {
            orderNumber: order.orderNumber,
            status: order.status
          },
          priority: this.getNotificationPriority(order.status)
        }
      ];

      // Add SMS for critical status updates
      if (this.isCriticalStatus(order.status)) {
        notifications.push({
          userId: order.userId.toString(),
          type: NotificationType.SMS,
          templateId: `${templateId}_SMS`,
          variables: {
            orderNumber: order.orderNumber,
            status: order.status
          },
          priority: 'HIGH'
        });
      }

      await this.sendBulkNotifications(notifications);

    } catch (error) {
      console.error('Failed to send order status update notification:', error);
    }
  }

  /**
   * Send inventory shortage notification
   */
  async sendInventoryShortageNotification(order: IOrder, item: IOrderItem): Promise<void> {
    try {
      const notification: NotificationRequest = {
        userId: order.userId.toString(),
        type: NotificationType.EMAIL,
        templateId: 'INVENTORY_SHORTAGE_EMAIL',
        variables: {
          orderNumber: order.orderNumber,
          productName: item.productSnapshot.name,
          requestedQuantity: item.quantity,
          productId: item.productId.toString()
        },
        priority: 'HIGH'
      };

      await this.sendNotification(notification);

    } catch (error) {
      console.error('Failed to send inventory shortage notification:', error);
    }
  }

  /**
   * Send delivery reminder notification
   */
  async sendDeliveryReminderNotification(order: IOrder): Promise<void> {
    try {
      const notifications: NotificationRequest[] = [
        {
          userId: order.userId.toString(),
          type: NotificationType.PUSH,
          templateId: 'DELIVERY_REMINDER_PUSH',
          variables: {
            orderNumber: order.orderNumber,
            estimatedDeliveryDate: order.estimatedDeliveryDate?.toDateString(),
            // No tracking for agricultural marketplace
          },
          priority: 'MEDIUM'
        }
      ];

      await this.sendBulkNotifications(notifications);

    } catch (error) {
      console.error('Failed to send delivery reminder notification:', error);
    }
  }

  /**
   * Send payment reminder notification
   */
  async sendPaymentReminderNotification(order: IOrder): Promise<void> {
    try {
      const notifications: NotificationRequest[] = [
        {
          userId: order.userId.toString(),
          type: NotificationType.EMAIL,
          templateId: 'PAYMENT_REMINDER_EMAIL',
          variables: {
            orderNumber: order.orderNumber,
            totalAmount: order.totalAmount,
            currency: order.currency,
            dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toDateString() // 24 hours from now
          },
          priority: 'HIGH'
        },
        {
          userId: order.userId.toString(),
          type: NotificationType.SMS,
          templateId: 'PAYMENT_REMINDER_SMS',
          variables: {
            orderNumber: order.orderNumber,
            totalAmount: order.totalAmount
          },
          priority: 'HIGH'
        }
      ];

      await this.sendBulkNotifications(notifications);

    } catch (error) {
      console.error('Failed to send payment reminder notification:', error);
    }
  }

  /**
   * Send seller order notification
   */
  private async sendSellerOrderNotification(sellerId: string, order: IOrder): Promise<void> {
    try {
      const sellerItems = order.items.filter(item => item.sellerId.toString() === sellerId);
      const sellerTotal = sellerItems.reduce((sum, item) => sum + item.totalPrice, 0);

      const notification: NotificationRequest = {
        userId: sellerId,
        type: NotificationType.EMAIL,
        templateId: 'NEW_ORDER_SELLER_EMAIL',
        variables: {
          orderNumber: order.orderNumber,
          itemCount: sellerItems.length,
          totalAmount: sellerTotal,
          currency: order.currency,
          // No shipping address for agricultural marketplace
        },
        priority: 'MEDIUM'
      };

      await this.sendNotification(notification);

    } catch (error) {
      console.error(`Failed to send seller notification to ${sellerId}:`, error);
    }
  }

  /**
   * Send single notification
   */
  private async sendNotification(notification: NotificationRequest): Promise<void> {
    try {
      await axios.post(`${this.notificationServiceUrl}/api/notifications/send`, notification);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new AppError(500, `Failed to send notification: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Send bulk notifications
   */
  private async sendBulkNotifications(notifications: NotificationRequest[]): Promise<void> {
    try {
      await axios.post(`${this.notificationServiceUrl}/api/notifications/send-bulk`, {
        notifications
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new AppError(500, `Failed to send bulk notifications: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Get status update template ID
   */
  private getStatusUpdateTemplateId(status: OrderStatus): string {
    const templateMap: { [key in OrderStatus]: string } = {
      [OrderStatus.PENDING]: 'ORDER_PENDING',
      [OrderStatus.CONFIRMED]: 'ORDER_CONFIRMED',
      [OrderStatus.PAID]: 'ORDER_PAID',
      [OrderStatus.PROCESSING]: 'ORDER_PROCESSING',
      [OrderStatus.HARVESTING]: 'ORDER_HARVESTING',
      [OrderStatus.QUALITY_CHECK]: 'ORDER_QUALITY_CHECK',
      [OrderStatus.PACKAGING]: 'ORDER_PACKAGING',
      [OrderStatus.SHIPPED]: 'ORDER_SHIPPED',
      [OrderStatus.IN_TRANSIT]: 'ORDER_IN_TRANSIT',
      [OrderStatus.DELIVERED]: 'ORDER_DELIVERED',
      [OrderStatus.COMPLETED]: 'ORDER_COMPLETED',
      [OrderStatus.CANCELLED]: 'ORDER_CANCELLED',
      [OrderStatus.REFUNDED]: 'ORDER_REFUNDED',
      [OrderStatus.PAYMENT_FAILED]: 'PAYMENT_FAILED',
      [OrderStatus.DISPUTED]: 'ORDER_DISPUTED'
    };

    return templateMap[status] || 'ORDER_STATUS_UPDATE';
  }

  /**
   * Get notification priority based on status
   */
  private getNotificationPriority(status: OrderStatus): 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' {
    const highPriorityStatuses = [
      OrderStatus.PAYMENT_FAILED,
      OrderStatus.CANCELLED,
      OrderStatus.DISPUTED,
      OrderStatus.DELIVERED
    ];

    const mediumPriorityStatuses = [
      OrderStatus.CONFIRMED,
      OrderStatus.PAID,
      OrderStatus.SHIPPED,
      OrderStatus.COMPLETED
    ];

    if (highPriorityStatuses.includes(status)) {
      return 'HIGH';
    } else if (mediumPriorityStatuses.includes(status)) {
      return 'MEDIUM';
    } else {
      return 'LOW';
    }
  }

  /**
   * Check if status is critical
   */
  private isCriticalStatus(status: OrderStatus): boolean {
    const criticalStatuses = [
      OrderStatus.PAYMENT_FAILED,
      OrderStatus.CANCELLED,
      OrderStatus.DISPUTED,
      OrderStatus.DELIVERED,
      OrderStatus.SHIPPED
    ];

    return criticalStatuses.includes(status);
  }

  /**
   * Load notification templates
   */
  private loadNotificationTemplates(): void {
    // In real implementation, this would load from database or config service
    const templates: NotificationTemplate[] = [
      {
        id: 'ORDER_CREATED_EMAIL',
        type: NotificationType.EMAIL,
        subject: 'Order Confirmation - {{orderNumber}}',
        title: 'Order Confirmed',
        body: 'Your order {{orderNumber}} has been confirmed. Total amount: {{currency}} {{totalAmount}}',
        variables: ['orderNumber', 'totalAmount', 'currency']
      },
      {
        id: 'ORDER_SHIPPED_PUSH',
        type: NotificationType.PUSH,
        title: 'Order Shipped',
        body: 'Your order {{orderNumber}} has been shipped. Track: {{trackingNumber}}',
        variables: ['orderNumber', 'trackingNumber']
      }
      // Add more templates as needed
    ];

    templates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * Schedule notification
   */
  async scheduleNotification(notification: NotificationRequest, scheduledAt: Date): Promise<void> {
    try {
      notification.scheduledAt = scheduledAt;
      await axios.post(`${this.notificationServiceUrl}/api/notifications/schedule`, notification);
    } catch (error) {
      console.error('Failed to schedule notification:', error);
    }
  }

  /**
   * Send custom notification
   */
  async sendCustomNotification(
    userId: string,
    type: NotificationType,
    title: string,
    body: string,
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' = 'MEDIUM'
  ): Promise<void> {
    try {
      const notification: NotificationRequest = {
        userId,
        type,
        templateId: 'CUSTOM',
        variables: { title, body },
        priority
      };

      await this.sendNotification(notification);
    } catch (error) {
      console.error('Failed to send custom notification:', error);
    }
  }
}

export default NotificationService;
