import { IOrder, PaymentStatus } from '../models/order.model';
import { AppError } from '../utils/error-handler';
import axios from 'axios';

/**
 * Payment method types
 */
export enum PaymentMethod {
  CREDIT_CARD = 'CREDIT_CARD',
  DEBIT_CARD = 'DEBIT_CARD',
  UPI = 'UPI',
  NET_BANKING = 'NET_BANKING',
  WALLET = 'WALLET',
  COD = 'COD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  ESCROW = 'ESCROW'
}

/**
 * Payment gateway types
 */
export enum PaymentGateway {
  RAZORPAY = 'RAZORPAY',
  STRIPE = 'STRIPE',
  PAYU = 'PAYU',
  CASHFREE = 'CASHFREE',
  PHONEPE = 'PHONEPE'
}

/**
 * Payment request interface
 */
export interface PaymentRequest {
  orderId: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  gateway: PaymentGateway;
  customerDetails: {
    name: string;
    email: string;
    phone: string;
  };
  metadata?: any;
}

/**
 * Payment response interface
 */
export interface PaymentResponse {
  success: boolean;
  paymentId: string;
  transactionId?: string;
  status: PaymentStatus;
  gatewayResponse: any;
  redirectUrl?: string;
  errorMessage?: string;
}

/**
 * Installment plan interface
 */
export interface InstallmentPlan {
  totalAmount: number;
  installmentCount: number;
  installmentAmount: number;
  frequency: 'WEEKLY' | 'MONTHLY' | 'QUARTERLY';
  interestRate: number;
  processingFee: number;
  schedule: InstallmentSchedule[];
}

/**
 * Installment schedule interface
 */
export interface InstallmentSchedule {
  installmentNumber: number;
  amount: number;
  dueDate: Date;
  status: 'PENDING' | 'PAID' | 'OVERDUE' | 'FAILED';
  paidDate?: Date;
  transactionId?: string;
}

/**
 * Escrow details interface
 */
export interface EscrowDetails {
  escrowId: string;
  amount: number;
  releaseConditions: string[];
  milestones: EscrowMilestone[];
  status: 'CREATED' | 'FUNDED' | 'RELEASED' | 'DISPUTED' | 'REFUNDED';
}

/**
 * Escrow milestone interface
 */
export interface EscrowMilestone {
  id: string;
  description: string;
  amount: number;
  status: 'PENDING' | 'COMPLETED' | 'DISPUTED';
  completedDate?: Date;
  evidence?: string[];
}

/**
 * Payment service for handling various payment methods and gateways
 */
export class PaymentService {
  private paymentServiceUrl: string;
  private gatewayConfigs: Map<PaymentGateway, any>;

  constructor() {
    this.paymentServiceUrl = process.env.PAYMENT_SERVICE_URL || 'http://localhost:3006';
    this.gatewayConfigs = new Map();
    this.loadGatewayConfigs();
  }

  /**
   * Initiate payment for an order
   */
  async initiatePayment(order: IOrder, paymentRequest: PaymentRequest): Promise<PaymentResponse> {
    try {
      // Validate payment request
      this.validatePaymentRequest(paymentRequest);

      // Choose appropriate gateway based on method and amount
      const gateway = this.selectOptimalGateway(paymentRequest);

      // Create payment with gateway
      const response = await this.createPaymentWithGateway(gateway, paymentRequest);

      // Update order payment details
      await this.updateOrderPaymentDetails(order, response);

      return response;

    } catch (error) {
      throw new AppError(500, `Payment initiation failed: ${error.message}`);
    }
  }

  /**
   * Process payment callback/webhook
   */
  async processPaymentCallback(
    paymentId: string, 
    gatewayResponse: any, 
    gateway: PaymentGateway
  ): Promise<{
    orderId: string;
    status: PaymentStatus;
    transactionId?: string;
  }> {
    try {
      // Verify webhook signature
      const isValid = await this.verifyWebhookSignature(gateway, gatewayResponse);
      if (!isValid) {
        throw new AppError(400, 'Invalid webhook signature');
      }

      // Parse gateway response
      const paymentStatus = this.parseGatewayStatus(gateway, gatewayResponse);
      
      // Get order details
      const orderDetails = await this.getOrderByPaymentId(paymentId);

      return {
        orderId: orderDetails.orderId,
        status: paymentStatus,
        transactionId: gatewayResponse.transactionId || gatewayResponse.txnId
      };

    } catch (error) {
      throw new AppError(500, `Payment callback processing failed: ${error.message}`);
    }
  }

  /**
   * Create installment plan for high-value orders
   */
  async createInstallmentPlan(
    order: IOrder, 
    installmentCount: number, 
    frequency: 'WEEKLY' | 'MONTHLY' | 'QUARTERLY'
  ): Promise<InstallmentPlan> {
    try {
      const totalAmount = order.totalAmount;
      const interestRate = this.calculateInterestRate(totalAmount, installmentCount);
      const processingFee = this.calculateProcessingFee(totalAmount);
      
      const totalWithInterest = totalAmount + (totalAmount * interestRate / 100) + processingFee;
      const installmentAmount = Math.ceil(totalWithInterest / installmentCount);

      const schedule = this.generateInstallmentSchedule(
        installmentCount, 
        installmentAmount, 
        frequency
      );

      const plan: InstallmentPlan = {
        totalAmount: totalWithInterest,
        installmentCount,
        installmentAmount,
        frequency,
        interestRate,
        processingFee,
        schedule
      };

      // Save installment plan
      await this.saveInstallmentPlan(order.id, plan);

      return plan;

    } catch (error) {
      throw new AppError(500, `Failed to create installment plan: ${error.message}`);
    }
  }

  /**
   * Create escrow for high-value transactions
   */
  async createEscrow(
    order: IOrder, 
    releaseConditions: string[], 
    milestones: Omit<EscrowMilestone, 'id' | 'status'>[]
  ): Promise<EscrowDetails> {
    try {
      const escrowId = this.generateEscrowId();
      
      const escrowMilestones: EscrowMilestone[] = milestones.map((milestone, index) => ({
        id: `${escrowId}-M${index + 1}`,
        ...milestone,
        status: 'PENDING'
      }));

      const escrowDetails: EscrowDetails = {
        escrowId,
        amount: order.totalAmount,
        releaseConditions,
        milestones: escrowMilestones,
        status: 'CREATED'
      };

      // Create escrow with payment service
      await this.createEscrowWithService(escrowDetails);

      // Update order with escrow details
      order.paymentDetails.escrowDetails = {
        escrowId,
        releaseConditions,
        isReleased: false
      };

      return escrowDetails;

    } catch (error) {
      throw new AppError(500, `Failed to create escrow: ${error.message}`);
    }
  }

  /**
   * Process refund
   */
  async processRefund(
    order: IOrder, 
    amount?: number, 
    reason?: string
  ): Promise<{
    success: boolean;
    refundId: string;
    amount: number;
    status: string;
  }> {
    try {
      const refundAmount = amount || order.totalAmount;
      
      if (!order.paymentDetails.paymentId) {
        throw new AppError(400, 'No payment found for this order');
      }

      const refundRequest = {
        paymentId: order.paymentDetails.paymentId,
        amount: refundAmount,
        reason: reason || 'Order cancellation',
        orderId: order.id
      };

      const response = await axios.post(
        `${this.paymentServiceUrl}/api/payments/refund`,
        refundRequest
      );

      // Update order payment status
      if (refundAmount === order.totalAmount) {
        order.paymentDetails.status = PaymentStatus.REFUNDED;
      } else {
        order.paymentDetails.status = PaymentStatus.PARTIALLY_REFUNDED;
      }

      return response.data;

    } catch (error) {
      throw new AppError(500, `Refund processing failed: ${error.message}`);
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(paymentId: string): Promise<{
    status: PaymentStatus;
    transactionId?: string;
    amount: number;
    currency: string;
    method: string;
    gateway: string;
    createdAt: Date;
    updatedAt: Date;
  }> {
    try {
      const response = await axios.get(
        `${this.paymentServiceUrl}/api/payments/${paymentId}/status`
      );
      return response.data;
    } catch (error) {
      throw new AppError(500, `Failed to get payment status: ${error.message}`);
    }
  }

  /**
   * Validate payment method for order
   */
  validatePaymentMethodForOrder(order: IOrder, method: PaymentMethod): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if COD is available (no shipping address for agricultural marketplace)
    if (method === PaymentMethod.COD) {
      // COD available for agricultural marketplace by default
      if (order.totalAmount > 50000) {
        errors.push('Cash on Delivery not available for orders above ₹50,000');
      }
    }

    // Check if escrow is required for high-value transactions
    if (order.totalAmount > 100000 && method !== PaymentMethod.ESCROW) {
      warnings.push('Escrow payment recommended for high-value transactions');
    }

    // Check for agricultural product specific requirements
    const hasPerishableItems = order.items.some(item => 
      item.productType === 'CROP' && item.storageRequirements?.includes('perishable')
    );
    
    if (hasPerishableItems && method === PaymentMethod.COD) {
      warnings.push('Advance payment recommended for perishable items');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Private helper methods
   */
  private validatePaymentRequest(request: PaymentRequest): void {
    if (!request.orderId || !request.amount || !request.currency) {
      throw new AppError(400, 'Missing required payment parameters');
    }

    if (request.amount <= 0) {
      throw new AppError(400, 'Payment amount must be greater than zero');
    }
  }

  private selectOptimalGateway(request: PaymentRequest): PaymentGateway {
    // Logic to select best gateway based on method, amount, and success rates
    if (request.method === PaymentMethod.UPI) {
      return PaymentGateway.PHONEPE;
    } else if (request.amount > 100000) {
      return PaymentGateway.RAZORPAY; // Better for high-value transactions
    } else {
      return PaymentGateway.CASHFREE; // Better rates for smaller amounts
    }
  }

  private async createPaymentWithGateway(
    gateway: PaymentGateway, 
    request: PaymentRequest
  ): Promise<PaymentResponse> {
    const response = await axios.post(
      `${this.paymentServiceUrl}/api/payments/create`,
      {
        ...request,
        gateway
      }
    );
    return response.data;
  }

  private async updateOrderPaymentDetails(order: IOrder, response: PaymentResponse): Promise<void> {
    order.paymentDetails.paymentId = response.paymentId;
    order.paymentDetails.status = response.status;
    order.paymentDetails.gatewayResponse = response.gatewayResponse;
  }

  private async verifyWebhookSignature(gateway: PaymentGateway, response: any): Promise<boolean> {
    // Implement signature verification for each gateway
    return true; // Placeholder
  }

  private parseGatewayStatus(gateway: PaymentGateway, response: any): PaymentStatus {
    // Parse status from different gateways
    const statusMap: { [key: string]: PaymentStatus } = {
      'success': PaymentStatus.COMPLETED,
      'failed': PaymentStatus.FAILED,
      'pending': PaymentStatus.PROCESSING
    };

    return statusMap[response.status] || PaymentStatus.FAILED;
  }

  private async getOrderByPaymentId(paymentId: string): Promise<{ orderId: string }> {
    const response = await axios.get(
      `${this.paymentServiceUrl}/api/payments/${paymentId}/order`
    );
    return response.data;
  }

  private calculateInterestRate(amount: number, installments: number): number {
    // Calculate interest rate based on amount and installment count
    const baseRate = 12; // 12% annual
    const installmentFactor = installments > 6 ? 1.5 : 1;
    return baseRate * installmentFactor / 12; // Monthly rate
  }

  private calculateProcessingFee(amount: number): number {
    return Math.min(amount * 0.02, 1000); // 2% or max ₹1000
  }

  private generateInstallmentSchedule(
    count: number, 
    amount: number, 
    frequency: string
  ): InstallmentSchedule[] {
    const schedule: InstallmentSchedule[] = [];
    const frequencyDays = frequency === 'WEEKLY' ? 7 : frequency === 'MONTHLY' ? 30 : 90;

    for (let i = 1; i <= count; i++) {
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + (i * frequencyDays));

      schedule.push({
        installmentNumber: i,
        amount,
        dueDate,
        status: 'PENDING'
      });
    }

    return schedule;
  }

  private async saveInstallmentPlan(orderId: string, plan: InstallmentPlan): Promise<void> {
    await axios.post(
      `${this.paymentServiceUrl}/api/installments/create`,
      { orderId, plan }
    );
  }

  private generateEscrowId(): string {
    return `ESC-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
  }

  private async createEscrowWithService(escrowDetails: EscrowDetails): Promise<void> {
    await axios.post(
      `${this.paymentServiceUrl}/api/escrow/create`,
      escrowDetails
    );
  }

  private isCODAvailable(address: any): boolean {
    // Check if COD is available for the given address
    const codAvailableStates = ['Maharashtra', 'Karnataka', 'Tamil Nadu', 'Gujarat'];
    return codAvailableStates.includes(address.state);
  }

  private loadGatewayConfigs(): void {
    // Load gateway configurations
    this.gatewayConfigs.set(PaymentGateway.RAZORPAY, {
      apiKey: process.env.RAZORPAY_API_KEY,
      apiSecret: process.env.RAZORPAY_API_SECRET
    });
    
    this.gatewayConfigs.set(PaymentGateway.CASHFREE, {
      appId: process.env.CASHFREE_APP_ID,
      secretKey: process.env.CASHFREE_SECRET_KEY
    });
  }
}

export default PaymentService;
