import OrderModel, {
  <PERSON><PERSON>rder,
  IOrderItem,
  OrderStatus,
  PaymentStatus,
  ProductType,
  DeliveryMethod,
  ShippingAddress,
  PaymentDetails
} from '../models/order.model';
import {
    publishOrderCreatedEvent,
    publishOrderPaidEvent,
    publishOrderCancelledEvent,
    publishOrderShippedEvent,
    publishOrderDeliveredEvent,
    publishOrderStatusUpdatedEvent
 } from '../events/publishers/order.publisher';
import { AppError } from '../utils/error-handler';
import { CreateOrderInput, UpdateOrderInput, OrderQueryOptions } from '../validators/order.validator';
import { ProductService } from './product.service';
import { InventoryService } from './inventory.service';
import { PricingService } from './pricing.service';
import { NotificationService } from './notification.service';
import mongoose from 'mongoose';

/**
 * Order creation result interface
 */
export interface OrderCreationResult {
  order: IOrder;
  warnings?: string[];
  inventoryReserved: boolean;
}

/**
 * Order statistics interface
 */
export interface OrderStatistics {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  statusBreakdown: { [key in OrderStatus]?: number };
  monthlyTrends: {
    month: string;
    orders: number;
    revenue: number;
  }[];
}

/**
 * Order validation result interface
 */
export interface OrderValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  estimatedTotal: number;
  availabilityIssues: {
    productId: string;
    requestedQuantity: number;
    availableQuantity: number;
  }[];
}

/**
 * Comprehensive Order Service for Agricultural Products
 */
export class OrderService {
  private productService: ProductService;
  private inventoryService: InventoryService;
  private pricingService: PricingService;
  private notificationService: NotificationService;

  constructor() {
    this.productService = new ProductService();
    this.inventoryService = new InventoryService();
    this.pricingService = new PricingService();
    this.notificationService = new NotificationService();
  }

  /**
   * Create a new order with comprehensive validation and processing
   */
  async createOrder(userId: string, orderData: CreateOrderInput): Promise<OrderCreationResult> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate order data
      const validation = await this.validateOrder(orderData);
      if (!validation.isValid) {
        throw new AppError(400, `Order validation failed: ${validation.errors.join(', ')}`);
      }

      // Process order items and get product details
      const processedItems = await this.processOrderItems(orderData.items);

      // Calculate pricing
      const pricing = await this.pricingService.calculateOrderPricing(processedItems, orderData);

      // Reserve inventory
      const inventoryReserved = await this.inventoryService.reserveInventory(processedItems, session);

      // Create order document
      const order = new OrderModel({
        userId: new mongoose.Types.ObjectId(userId),
        items: processedItems,
        subtotal: pricing.subtotal,
        taxes: pricing.taxes,
        shippingCharges: pricing.shippingCharges,
        discounts: pricing.discounts,
        totalAmount: pricing.totalAmount,
        currency: pricing.currency,
        status: OrderStatus.PENDING,
        // No shipping/delivery for agricultural marketplace
        paymentDetails: {
          method: orderData.paymentMethod,
          status: PaymentStatus.PENDING,
          amount: pricing.totalAmount,
          currency: pricing.currency
        },
        buyerNotes: orderData.buyerNotes,
        seasonalInfo: orderData.seasonalInfo,
        qualityAssurance: orderData.qualityAssurance
      });

      // Add initial status to history
      order.addStatusUpdate(OrderStatus.PENDING, 'Order created', 'system');

      await order.save({ session });
      await session.commitTransaction();

      // Publish order created event
      await publishOrderCreatedEvent(order);

      // Send notifications
      await this.notificationService.sendOrderCreatedNotification(order);

      return {
        order,
        warnings: validation.warnings,
        inventoryReserved
      };

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Validate order before creation
   */
  async validateOrder(orderData: CreateOrderInput): Promise<OrderValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const availabilityIssues: any[] = [];
    let estimatedTotal = 0;

    // Validate items
    if (!orderData.items || orderData.items.length === 0) {
      errors.push('Order must contain at least one item');
    }

    // Check product availability and pricing
    for (const item of orderData.items || []) {
      try {
        const product = await this.productService.getProductById(item.productId);
        if (!product) {
          errors.push(`Product ${item.productId} not found`);
          continue;
        }

        // Check availability
        const availability = await this.inventoryService.checkAvailability(item.productId, item.quantity);
        if (!availability.isAvailable) {
          availabilityIssues.push({
            productId: item.productId,
            requestedQuantity: item.quantity,
            availableQuantity: availability.availableQuantity
          });
          errors.push(`Insufficient stock for product ${product.name}`);
        }

        // Validate pricing
        const currentPrice = await this.pricingService.getCurrentPrice(item.productId);
        if (Math.abs(currentPrice - item.unitPrice) > currentPrice * 0.05) { // 5% tolerance
          warnings.push(`Price for ${product.name} has changed since last viewed`);
        }

        estimatedTotal += currentPrice * item.quantity;

      } catch (error) {
        errors.push(`Error validating item ${item.productId}: ${error.message}`);
      }
    }

    // No shipping validation needed for agricultural marketplace

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      estimatedTotal,
      availabilityIssues
    };
  }

  /**
   * Process order items and enrich with product data
   */
  private async processOrderItems(items: any[]): Promise<IOrderItem[]> {
    const processedItems: IOrderItem[] = [];

    for (const item of items) {
      const product = await this.productService.getProductById(item.productId);
      if (!product) {
        throw new AppError(404, `Product ${item.productId} not found`);
      }

      const processedItem: IOrderItem = {
        productId: new mongoose.Types.ObjectId(item.productId),
        sellerId: new mongoose.Types.ObjectId(product.sellerId),
        productType: product.type as ProductType,
        productSnapshot: {
          name: product.name,
          description: product.description,
          category: product.category,
          subCategory: product.subCategory,
          variety: product.attributes?.variety,
          farmingMethod: product.attributes?.farmingMethod,
          images: product.attributes?.images || []
        },
        unitPrice: item.unitPrice,
        quantity: item.quantity,
        unit: item.unit || 'pieces',
        totalPrice: item.unitPrice * item.quantity,
        qualityRequirements: item.qualityRequirements,
        harvestDate: item.harvestDate ? new Date(item.harvestDate) : undefined,
        expiryDate: item.expiryDate ? new Date(item.expiryDate) : undefined,
        storageRequirements: item.storageRequirements,
        ownershipPercentage: item.ownershipPercentage,
        expectedYield: item.expectedYield,
        yieldUnit: item.yieldUnit,
        serviceDate: item.serviceDate ? new Date(item.serviceDate) : undefined,
        serviceDuration: item.serviceDuration,
        serviceArea: item.serviceArea,
        rentalPeriod: item.rentalPeriod ? {
          startDate: new Date(item.rentalPeriod.startDate),
          endDate: new Date(item.rentalPeriod.endDate)
        } : undefined,
        itemStatus: OrderStatus.PENDING,
        statusHistory: [{
          status: OrderStatus.PENDING,
          timestamp: new Date(),
          notes: 'Item added to order'
        }]
      };

      processedItems.push(processedItem);
    }

    return processedItems;
  }

  /**
   * Get order by ID with optional population
   */
  async getOrderById(orderId: string, populateFields?: string[]): Promise<IOrder | null> {
    let query = OrderModel.findById(orderId);

    if (populateFields) {
      populateFields.forEach(field => {
        query = query.populate(field);
      });
    }

    return query.exec();
  }

  /**
   * Get orders for a user with filtering and pagination
   */
  async getUserOrders(userId: string, options: OrderQueryOptions = {}): Promise<{
    orders: IOrder[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      startDate,
      endDate
    } = options;

    const query: any = { userId: new mongoose.Types.ObjectId(userId) };

    // Add filters
    if (status) {
      query.status = status;
    }

    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Execute query with pagination
    const [orders, total] = await Promise.all([
      OrderModel.find(query)
        .sort({ [sortBy]: sortOrder === 'desc' ? -1 : 1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec(),
      OrderModel.countDocuments(query)
    ]);

    return {
      orders,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Get orders for a seller
   */
  async getSellerOrders(sellerId: string, options: OrderQueryOptions = {}): Promise<{
    orders: IOrder[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options;

    const query: any = { 'items.sellerId': new mongoose.Types.ObjectId(sellerId) };

    if (status) {
      query.status = status;
    }

    const [orders, total] = await Promise.all([
      OrderModel.find(query)
        .sort({ [sortBy]: sortOrder === 'desc' ? -1 : 1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec(),
      OrderModel.countDocuments(query)
    ]);

    return {
      orders,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Update order status with validation and business logic
   */
  async updateOrderStatus(
    orderId: string,
    newStatus: OrderStatus,
    notes?: string,
    updatedBy?: string
  ): Promise<IOrder> {
    const order = await OrderModel.findById(orderId);
    if (!order) {
      throw new AppError(404, 'Order not found');
    }

    // Validate status transition
    this.validateStatusTransition(order.status, newStatus);

    // Update order status
    order.addStatusUpdate(newStatus, notes, updatedBy);

    // Handle status-specific business logic
    await this.handleStatusChange(order, newStatus);

    await order.save();

    // Publish status update event
    await publishOrderStatusUpdatedEvent(order);

    // Send notifications
    await this.notificationService.sendOrderStatusUpdateNotification(order);

    return order;
  }

  /**
   * Validate status transitions
   */
  private validateStatusTransition(currentStatus: OrderStatus, newStatus: OrderStatus): void {
    const validTransitions: { [key in OrderStatus]?: OrderStatus[] } = {
      [OrderStatus.PENDING]: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED, OrderStatus.PAYMENT_FAILED],
      [OrderStatus.CONFIRMED]: [OrderStatus.PAID, OrderStatus.CANCELLED],
      [OrderStatus.PAID]: [OrderStatus.PROCESSING, OrderStatus.CANCELLED, OrderStatus.REFUNDED],
      [OrderStatus.PROCESSING]: [OrderStatus.HARVESTING, OrderStatus.PACKAGING, OrderStatus.SHIPPED],
      [OrderStatus.HARVESTING]: [OrderStatus.QUALITY_CHECK, OrderStatus.PACKAGING],
      [OrderStatus.QUALITY_CHECK]: [OrderStatus.PACKAGING, OrderStatus.DISPUTED],
      [OrderStatus.PACKAGING]: [OrderStatus.SHIPPED],
      [OrderStatus.SHIPPED]: [OrderStatus.IN_TRANSIT, OrderStatus.DELIVERED],
      [OrderStatus.IN_TRANSIT]: [OrderStatus.DELIVERED],
      [OrderStatus.DELIVERED]: [OrderStatus.COMPLETED],
      [OrderStatus.COMPLETED]: [], // Final state
      [OrderStatus.CANCELLED]: [], // Final state
      [OrderStatus.REFUNDED]: [], // Final state
      [OrderStatus.PAYMENT_FAILED]: [OrderStatus.PENDING, OrderStatus.CANCELLED],
      [OrderStatus.DISPUTED]: [OrderStatus.COMPLETED, OrderStatus.REFUNDED]
    };

    const allowedTransitions = validTransitions[currentStatus] || [];
    if (!allowedTransitions.includes(newStatus)) {
      throw new AppError(400, `Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }

  /**
   * Handle status-specific business logic
   */
  private async handleStatusChange(order: IOrder, newStatus: OrderStatus): Promise<void> {
    switch (newStatus) {
      case OrderStatus.PAID:
        await this.handleOrderPaid(order);
        break;
      case OrderStatus.SHIPPED:
        await this.handleOrderShipped(order);
        break;
      case OrderStatus.DELIVERED:
        await this.handleOrderDelivered(order);
        break;
      case OrderStatus.CANCELLED:
        await this.handleOrderCancelled(order);
        break;
      case OrderStatus.COMPLETED:
        await this.handleOrderCompleted(order);
        break;
    }
  }

  /**
   * Handle order paid logic
   */
  private async handleOrderPaid(order: IOrder): Promise<void> {
    // Update payment status
    order.paymentDetails.status = PaymentStatus.COMPLETED;

    // Confirm inventory reservation
    await this.inventoryService.confirmReservation(order.items);

    // Calculate estimated delivery date
    order.estimatedDeliveryDate = await this.calculateEstimatedDeliveryDate(order);

    await publishOrderPaidEvent(order);
  }

  /**
   * Handle order shipped logic
   */
  private async handleOrderShipped(order: IOrder): Promise<void> {
    // No tracking for agricultural marketplace
    await publishOrderShippedEvent(order);
  }

  /**
   * Handle order delivered logic
   */
  private async handleOrderDelivered(order: IOrder): Promise<void> {
    order.actualDeliveryDate = new Date();
    await publishOrderDeliveredEvent(order);
  }

  /**
   * Handle order cancelled logic
   */
  private async handleOrderCancelled(order: IOrder): Promise<void> {
    order.cancelledAt = new Date();

    // Release inventory reservation
    await this.inventoryService.releaseReservation(order.items);

    // Process refund if payment was made
    if (order.paymentDetails.status === PaymentStatus.COMPLETED) {
      await this.processRefund(order);
    }

    await publishOrderCancelledEvent(order);
  }

  /**
   * Handle order completed logic
   */
  private async handleOrderCompleted(order: IOrder): Promise<void> {
    order.completedAt = new Date();

    // Update inventory with actual consumption
    await this.inventoryService.updateInventoryAfterCompletion(order.items);
  }

  /**
   * Cancel an order with proper validation
   */
  async cancelOrder(orderId: string, userId: string, reason?: string): Promise<IOrder> {
    const order = await OrderModel.findOne({ _id: orderId, userId });

    if (!order) {
      throw new AppError(404, 'Order not found');
    }

    if (!order.canBeCancelled()) {
      throw new AppError(400, `Order cannot be cancelled in ${order.status} status`);
    }

    order.cancellationReason = reason;
    await this.updateOrderStatus(orderId, OrderStatus.CANCELLED, `Order cancelled: ${reason}`, userId);

    return order;
  }

  /**
   * Update order details (before confirmation)
   */
  async updateOrder(orderId: string, userId: string, updateData: UpdateOrderInput): Promise<IOrder> {
    const order = await OrderModel.findOne({ _id: orderId, userId });

    if (!order) {
      throw new AppError(404, 'Order not found');
    }

    if (!order.canBeModified()) {
      throw new AppError(400, `Order cannot be modified in ${order.status} status`);
    }

    // Update allowed fields (no shipping/delivery for agricultural marketplace)

    if (updateData.buyerNotes) {
      order.buyerNotes = updateData.buyerNotes;
    }

    await order.save();
    return order;
  }

  /**
   * Add rating and review to order
   */
  async addOrderRating(
    orderId: string,
    userId: string,
    rating: number,
    review?: string
  ): Promise<IOrder> {
    const order = await OrderModel.findOne({ _id: orderId, userId });

    if (!order) {
      throw new AppError(404, 'Order not found');
    }

    if (!order.isDelivered()) {
      throw new AppError(400, 'Order must be delivered before rating');
    }

    if (order.buyerRating) {
      throw new AppError(400, 'Order has already been rated');
    }

    order.buyerRating = {
      rating,
      review,
      ratedAt: new Date()
    };

    await order.save();

    // Update seller rating
    await this.updateSellerRating(order.items[0].sellerId.toString(), rating);

    return order;
  }

  /**
   * Get order statistics for a user
   */
  async getUserOrderStatistics(userId: string): Promise<OrderStatistics> {
    const stats = await OrderModel.aggregate([
      { $match: { userId: new mongoose.Types.ObjectId(userId) } },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalRevenue: { $sum: '$totalAmount' },
          statusBreakdown: {
            $push: '$status'
          }
        }
      }
    ]);

    const monthlyStats = await OrderModel.aggregate([
      { $match: { userId: new mongoose.Types.ObjectId(userId) } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          orders: { $sum: 1 },
          revenue: { $sum: '$totalAmount' }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } },
      { $limit: 12 }
    ]);

    const result = stats[0] || { totalOrders: 0, totalRevenue: 0, statusBreakdown: [] };

    // Process status breakdown
    const statusCounts: { [key in OrderStatus]?: number } = {};
    result.statusBreakdown.forEach((status: OrderStatus) => {
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    return {
      totalOrders: result.totalOrders,
      totalRevenue: result.totalRevenue,
      averageOrderValue: result.totalOrders > 0 ? result.totalRevenue / result.totalOrders : 0,
      statusBreakdown: statusCounts,
      monthlyTrends: monthlyStats.map(stat => ({
        month: `${stat._id.year}-${stat._id.month.toString().padStart(2, '0')}`,
        orders: stat.orders,
        revenue: stat.revenue
      }))
    };
  }

  /**
   * Utility methods
   */
  private async calculateEstimatedDeliveryDate(order: IOrder): Promise<Date> {
    const baseDeliveryDays = this.getBaseDeliveryDays(DeliveryMethod.STANDARD_DELIVERY); // Default for agricultural marketplace
    const processingDays = this.getProcessingDays(order.items);

    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + baseDeliveryDays + processingDays);

    return estimatedDate;
  }

  private getBaseDeliveryDays(method: DeliveryMethod): number {
    const deliveryDays = {
      [DeliveryMethod.PICKUP]: 0,
      [DeliveryMethod.STANDARD_DELIVERY]: 3,
      [DeliveryMethod.EXPRESS_DELIVERY]: 1,
      [DeliveryMethod.COLD_CHAIN]: 2,
      [DeliveryMethod.BULK_TRANSPORT]: 5
    };
    return deliveryDays[method] || 3;
  }

  private getProcessingDays(items: IOrderItem[]): number {
    let maxProcessingDays = 0;

    items.forEach(item => {
      let processingDays = 1; // Default

      switch (item.productType) {
        case ProductType.CROP:
          processingDays = item.harvestDate ? 0 : 7; // If not harvested yet
          break;
        case ProductType.FARM_SERVICE:
          processingDays = 0; // Services are immediate
          break;
        case ProductType.EQUIPMENT:
          processingDays = 2; // Equipment preparation
          break;
        default:
          processingDays = 1;
      }

      maxProcessingDays = Math.max(maxProcessingDays, processingDays);
    });

    return maxProcessingDays;
  }

  private async generateTrackingNumber(order: IOrder): Promise<string> {
    const prefix = 'AGR';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  private async processRefund(order: IOrder): Promise<void> {
    // Implement refund logic based on payment method
    // This would integrate with payment gateway
    order.paymentDetails.status = PaymentStatus.REFUNDED;
  }

  private async updateSellerRating(sellerId: string, rating: number): Promise<void> {
    // Update seller's overall rating
    // This would typically be handled by the user service
  }

  /**
   * Event handlers for external events
   */
  async handlePaymentProcessed(orderId: string, paymentDetails: any): Promise<void> {
    try {
      const order = await OrderModel.findById(orderId);
      if (!order) {
        throw new AppError(404, 'Order not found');
      }

      // Update payment details
      order.paymentDetails.paymentId = paymentDetails.paymentId;
      order.paymentDetails.transactionId = paymentDetails.transactionId;
      order.paymentDetails.gatewayResponse = paymentDetails.gatewayResponse;

      await this.updateOrderStatus(orderId, OrderStatus.PAID, 'Payment processed successfully');
    } catch (error) {
      console.error(`Error handling payment processed for order ${orderId}:`, error);
      throw error;
    }
  }

  async handlePaymentFailed(orderId: string, failureReason?: string): Promise<void> {
    try {
      await this.updateOrderStatus(
        orderId,
        OrderStatus.PAYMENT_FAILED,
        `Payment failed: ${failureReason || 'Unknown reason'}`
      );
    } catch (error) {
      console.error(`Error handling payment failed for order ${orderId}:`, error);
      throw error;
    }
  }

  async handleInventoryUpdated(productId: string, newQuantity: number): Promise<void> {
    // Handle inventory updates that might affect pending orders
    const pendingOrders = await OrderModel.find({
      'items.productId': productId,
      status: { $in: [OrderStatus.PENDING, OrderStatus.CONFIRMED] }
    });

    for (const order of pendingOrders) {
      const affectedItems = order.items.filter(item =>
        item.productId.toString() === productId
      );

      for (const item of affectedItems) {
        if (item.quantity > newQuantity) {
          // Notify about insufficient inventory
          await this.notificationService.sendInventoryShortageNotification(order, item);
        }
      }
    }
  }

  /**
   * Bulk operations
   */
  async bulkUpdateOrderStatus(
    orderIds: string[],
    newStatus: OrderStatus,
    notes?: string
  ): Promise<IOrder[]> {
    const updatedOrders: IOrder[] = [];

    for (const orderId of orderIds) {
      try {
        const order = await this.updateOrderStatus(orderId, newStatus, notes);
        updatedOrders.push(order);
      } catch (error) {
        console.error(`Failed to update order ${orderId}:`, error);
      }
    }

    return updatedOrders;
  }

  /**
   * Search orders with advanced filters
   */
  async searchOrders(filters: any, options: OrderQueryOptions = {}): Promise<{
    orders: IOrder[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options;

    const query = this.buildSearchQuery(filters);

    const [orders, total] = await Promise.all([
      OrderModel.find(query)
        .sort({ [sortBy]: sortOrder === 'desc' ? -1 : 1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec(),
      OrderModel.countDocuments(query)
    ]);

    return {
      orders,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  private buildSearchQuery(filters: any): any {
    const query: any = {};

    if (filters.userId) {
      query.userId = new mongoose.Types.ObjectId(filters.userId);
    }

    if (filters.sellerId) {
      query['items.sellerId'] = new mongoose.Types.ObjectId(filters.sellerId);
    }

    if (filters.status) {
      query.status = Array.isArray(filters.status) ? { $in: filters.status } : filters.status;
    }

    if (filters.orderNumber) {
      query.orderNumber = new RegExp(filters.orderNumber, 'i');
    }

    if (filters.minAmount || filters.maxAmount) {
      query.totalAmount = {};
      if (filters.minAmount) query.totalAmount.$gte = filters.minAmount;
      if (filters.maxAmount) query.totalAmount.$lte = filters.maxAmount;
    }

    if (filters.startDate || filters.endDate) {
      query.createdAt = {};
      if (filters.startDate) query.createdAt.$gte = new Date(filters.startDate);
      if (filters.endDate) query.createdAt.$lte = new Date(filters.endDate);
    }

    if (filters.productType) {
      query['items.productType'] = filters.productType;
    }

    return query;
  }
}