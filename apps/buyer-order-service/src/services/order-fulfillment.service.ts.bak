import { IOrder, <PERSON><PERSON>rde<PERSON><PERSON><PERSON>, OrderStatus, ProductType, DeliveryMethod } from '../models/order.model';
import { NotificationService, NotificationType } from './notification.service';
import { OrderTrackingService } from './order-tracking.service';
import { AppError } from '../utils/error-handler';
import axios from 'axios';

/**
 * Fulfillment stage interface
 */
export interface FulfillmentStage {
  id: string;
  name: string;
  description: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'SKIPPED';
  startTime?: Date;
  endTime?: Date;
  estimatedDuration: number; // in hours
  dependencies: string[];
  assignedTo?: string;
  notes?: string;
  qualityChecks?: QualityCheck[];
}

/**
 * Quality check interface
 */
export interface QualityCheck {
  id: string;
  type: 'VISUAL' | 'WEIGHT' | 'MOISTURE' | 'PESTICIDE' | 'FRESHNESS' | 'PACKAGING';
  description: string;
  status: 'PENDING' | 'PASSED' | 'FAILED' | 'REQUIRES_RECHECK';
  checkedBy?: string;
  checkedAt?: Date;
  result?: any;
  images?: string[];
  notes?: string;
}

/**
 * Fulfillment plan interface
 */
export interface FulfillmentPlan {
  orderId: string;
  stages: FulfillmentStage[];
  estimatedCompletionTime: Date;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  specialInstructions: string[];
  resourceRequirements: ResourceRequirement[];
}

/**
 * Resource requirement interface
 */
export interface ResourceRequirement {
  type: 'EQUIPMENT' | 'PERSONNEL' | 'VEHICLE' | 'STORAGE';
  resource: string;
  quantity: number;
  duration: number;
  availability: 'AVAILABLE' | 'RESERVED' | 'UNAVAILABLE';
}

/**
 * Harvest schedule interface
 */
export interface HarvestSchedule {
  cropId: string;
  farmerId: string;
  plotId: string;
  scheduledDate: Date;
  estimatedQuantity: number;
  actualQuantity?: number;
  qualityGrade?: string;
  harvestMethod: 'MANUAL' | 'MECHANICAL' | 'HYBRID';
  weatherConditions?: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'DELAYED' | 'CANCELLED';
}

/**
 * Order fulfillment service for managing harvest schedules, delivery logistics, and quality assurance
 */
export class OrderFulfillmentService {
  private notificationService: NotificationService;
  private trackingService: OrderTrackingService;
  private fulfillmentServiceUrl: string;

  constructor() {
    this.notificationService = new NotificationService();
    this.trackingService = new OrderTrackingService();
    this.fulfillmentServiceUrl = process.env.FULFILLMENT_SERVICE_URL || 'http://localhost:3008';
  }

  /**
   * Create fulfillment plan for an order
   */
  async createFulfillmentPlan(order: IOrder): Promise<FulfillmentPlan> {
    try {
      const stages = await this.generateFulfillmentStages(order);
      const estimatedCompletionTime = this.calculateEstimatedCompletion(stages);
      const priority = this.determinePriority(order);
      const specialInstructions = this.generateSpecialInstructions(order);
      const resourceRequirements = await this.calculateResourceRequirements(order, stages);

      const plan: FulfillmentPlan = {
        orderId: order.id,
        stages,
        estimatedCompletionTime,
        priority,
        specialInstructions,
        resourceRequirements
      };

      // Save fulfillment plan
      await this.saveFulfillmentPlan(plan);

      // Schedule stages
      await this.scheduleStages(plan);

      return plan;

    } catch (error) {
      throw new AppError(500, `Failed to create fulfillment plan: ${error.message}`);
    }
  }

  /**
   * Execute fulfillment stage
   */
  async executeFulfillmentStage(
    orderId: string, 
    stageId: string, 
    assignedTo?: string
  ): Promise<FulfillmentStage> {
    try {
      const plan = await this.getFulfillmentPlan(orderId);
      const stage = plan.stages.find(s => s.id === stageId);
      
      if (!stage) {
        throw new AppError(404, 'Fulfillment stage not found');
      }

      // Check dependencies
      const dependenciesMet = await this.checkStageDependencies(plan, stage);
      if (!dependenciesMet) {
        throw new AppError(400, 'Stage dependencies not met');
      }

      // Start stage execution
      stage.status = 'IN_PROGRESS';
      stage.startTime = new Date();
      stage.assignedTo = assignedTo;

      // Execute stage-specific logic
      await this.executeStageLogic(orderId, stage);

      // Update tracking
      await this.trackingService.addTrackingEvent({
        id: `${orderId}-${stageId}`,
        orderId,
        status: this.mapStageToOrderStatus(stage.name),
        timestamp: new Date(),
        description: `Started ${stage.name}`,
        source: 'SYSTEM'
      });

      // Save updated plan
      await this.updateFulfillmentPlan(plan);

      return stage;

    } catch (error) {
      throw new AppError(500, `Failed to execute fulfillment stage: ${error.message}`);
    }
  }

  /**
   * Complete fulfillment stage
   */
  async completeFulfillmentStage(
    orderId: string, 
    stageId: string, 
    result: any
  ): Promise<FulfillmentStage> {
    try {
      const plan = await this.getFulfillmentPlan(orderId);
      const stage = plan.stages.find(s => s.id === stageId);
      
      if (!stage) {
        throw new AppError(404, 'Fulfillment stage not found');
      }

      // Complete stage
      stage.status = 'COMPLETED';
      stage.endTime = new Date();
      stage.notes = result.notes;

      // Process quality checks if any
      if (stage.qualityChecks) {
        await this.processQualityChecks(orderId, stage.qualityChecks, result.qualityResults);
      }

      // Update order status if needed
      const orderStatus = this.mapStageToOrderStatus(stage.name);
      if (orderStatus) {
        await this.updateOrderStatus(orderId, orderStatus);
      }

      // Check if all stages are completed
      const allCompleted = plan.stages.every(s => s.status === 'COMPLETED' || s.status === 'SKIPPED');
      if (allCompleted) {
        await this.completeFulfillment(orderId);
      } else {
        // Start next stage if dependencies are met
        await this.startNextStage(plan);
      }

      // Update tracking
      await this.trackingService.addTrackingEvent({
        id: `${orderId}-${stageId}-completed`,
        orderId,
        status: orderStatus || OrderStatus.PROCESSING,
        timestamp: new Date(),
        description: `Completed ${stage.name}`,
        source: 'SYSTEM'
      });

      // Save updated plan
      await this.updateFulfillmentPlan(plan);

      return stage;

    } catch (error) {
      throw new AppError(500, `Failed to complete fulfillment stage: ${error.message}`);
    }
  }

  /**
   * Schedule harvest for crop orders
   */
  async scheduleHarvest(order: IOrder): Promise<HarvestSchedule[]> {
    try {
      const cropItems = order.items.filter(item => item.productType === ProductType.CROP);
      const harvestSchedules: HarvestSchedule[] = [];

      for (const item of cropItems) {
        const schedule: HarvestSchedule = {
          cropId: item.productId.toString(),
          farmerId: item.sellerId.toString(),
          plotId: 'unknown', // TODO: Add plotId to order item or fetch from product service
          scheduledDate: item.harvestDate || this.calculateOptimalHarvestDate(item),
          estimatedQuantity: item.quantity,
          harvestMethod: this.determineHarvestMethod(item),
          status: 'SCHEDULED'
        };

        // Save harvest schedule
        await this.saveHarvestSchedule(schedule);
        harvestSchedules.push(schedule);

        // Notify farmer
        await this.notificationService.sendCustomNotification(
          item.sellerId.toString(),
          NotificationType.EMAIL,
          'Harvest Scheduled',
          `Harvest scheduled for ${item.productSnapshot.name} on ${schedule.scheduledDate.toDateString()}`,
          'MEDIUM'
        );
      }

      return harvestSchedules;

    } catch (error) {
      throw new AppError(500, `Failed to schedule harvest: ${error.message}`);
    }
  }

  /**
   * Manage delivery logistics
   */
  async manageDeliveryLogistics(order: IOrder): Promise<{
    deliveryPlan: any;
    estimatedDeliveryTime: Date;
    deliveryPartner: string;
    cost: number;
  }> {
    try {
      // Determine optimal delivery method
      const deliveryMethod = this.optimizeDeliveryMethod(order);
      
      // Calculate delivery route
      const deliveryRoute = await this.calculateDeliveryRoute(order);
      
      // Assign delivery partner
      const deliveryPartner = await this.assignDeliveryPartner(order, deliveryMethod);
      
      // Calculate costs
      const deliveryCost = await this.calculateDeliveryCost(order, deliveryRoute, deliveryMethod);
      
      // Estimate delivery time
      const estimatedDeliveryTime = this.estimateDeliveryTime(order, deliveryRoute, deliveryMethod);

      const deliveryPlan = {
        orderId: order.id,
        method: deliveryMethod,
        route: deliveryRoute,
        partner: deliveryPartner,
        cost: deliveryCost,
        estimatedTime: estimatedDeliveryTime,
        specialRequirements: this.getDeliverySpecialRequirements(order)
      };

      // Save delivery plan
      await this.saveDeliveryPlan(deliveryPlan);

      return {
        deliveryPlan,
        estimatedDeliveryTime,
        deliveryPartner: deliveryPartner.name,
        cost: deliveryCost
      };

    } catch (error) {
      throw new AppError(500, `Failed to manage delivery logistics: ${error.message}`);
    }
  }

  /**
   * Perform quality assurance checks
   */
  async performQualityAssurance(
    orderId: string, 
    itemId: string, 
    checks: QualityCheck[]
  ): Promise<{
    overallStatus: 'PASSED' | 'FAILED' | 'REQUIRES_RECHECK';
    checks: QualityCheck[];
    recommendations: string[];
  }> {
    try {
      const processedChecks: QualityCheck[] = [];
      const recommendations: string[] = [];
      let overallStatus: 'PASSED' | 'FAILED' | 'REQUIRES_RECHECK' = 'PASSED';

      for (const check of checks) {
        const result = await this.executeQualityCheck(orderId, itemId, check);
        processedChecks.push(result);

        if (result.status === 'FAILED') {
          overallStatus = 'FAILED';
          recommendations.push(`${check.type} check failed: ${result.notes}`);
        } else if (result.status === 'REQUIRES_RECHECK') {
          overallStatus = 'REQUIRES_RECHECK';
          recommendations.push(`${check.type} requires recheck: ${result.notes}`);
        }
      }

      // Save quality assurance results
      await this.saveQualityAssuranceResults(orderId, itemId, {
        overallStatus,
        checks: processedChecks,
        recommendations
      });

      // Update order if quality checks failed
      if (overallStatus === 'FAILED') {
        await this.handleQualityFailure(orderId, itemId, recommendations);
      }

      return {
        overallStatus,
        checks: processedChecks,
        recommendations
      };

    } catch (error) {
      throw new AppError(500, `Failed to perform quality assurance: ${error.message}`);
    }
  }

  /**
   * Private helper methods
   */
  private async generateFulfillmentStages(order: IOrder): Promise<FulfillmentStage[]> {
    const stages: FulfillmentStage[] = [];

    // Order confirmation stage
    stages.push({
      id: 'order-confirmation',
      name: 'Order Confirmation',
      description: 'Confirm order details and availability',
      status: 'PENDING',
      estimatedDuration: 2,
      dependencies: []
    });

    // Product-specific stages
    const hasCrops = order.items.some(item => item.productType === ProductType.CROP);
    const hasEquipment = order.items.some(item => item.productType === ProductType.EQUIPMENT);
    const hasServices = order.items.some(item => item.productType === ProductType.FARM_SERVICE);

    if (hasCrops) {
      stages.push(
        {
          id: 'harvest-scheduling',
          name: 'Harvest Scheduling',
          description: 'Schedule crop harvest based on order requirements',
          status: 'PENDING',
          estimatedDuration: 24,
          dependencies: ['order-confirmation']
        },
        {
          id: 'harvesting',
          name: 'Harvesting',
          description: 'Harvest crops according to schedule',
          status: 'PENDING',
          estimatedDuration: 48,
          dependencies: ['harvest-scheduling']
        },
        {
          id: 'quality-check',
          name: 'Quality Check',
          description: 'Perform quality assurance on harvested crops',
          status: 'PENDING',
          estimatedDuration: 4,
          dependencies: ['harvesting'],
          qualityChecks: [
            {
              id: 'visual-inspection',
              type: 'VISUAL',
              description: 'Visual inspection for defects and quality',
              status: 'PENDING'
            },
            {
              id: 'freshness-check',
              type: 'FRESHNESS',
              description: 'Check freshness and shelf life',
              status: 'PENDING'
            }
          ]
        }
      );
    }

    if (hasEquipment) {
      stages.push({
        id: 'equipment-preparation',
        name: 'Equipment Preparation',
        description: 'Prepare and test equipment before delivery',
        status: 'PENDING',
        estimatedDuration: 12,
        dependencies: ['order-confirmation']
      });
    }

    if (hasServices) {
      stages.push({
        id: 'service-scheduling',
        name: 'Service Scheduling',
        description: 'Schedule service delivery with customer',
        status: 'PENDING',
        estimatedDuration: 6,
        dependencies: ['order-confirmation']
      });
    }

    // Common stages
    stages.push(
      {
        id: 'packaging',
        name: 'Packaging',
        description: 'Package items for delivery',
        status: 'PENDING',
        estimatedDuration: 6,
        dependencies: hasCrops ? ['quality-check'] : ['order-confirmation']
      },
      {
        id: 'shipping',
        name: 'Shipping',
        description: 'Ship packaged items to customer',
        status: 'PENDING',
        estimatedDuration: 72,
        dependencies: ['packaging']
      }
    );

    return stages;
  }

  private calculateEstimatedCompletion(stages: FulfillmentStage[]): Date {
    const totalDuration = stages.reduce((total, stage) => total + stage.estimatedDuration, 0);
    return new Date(Date.now() + totalDuration * 60 * 60 * 1000);
  }

  private determinePriority(order: IOrder): 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' {
    // Determine priority based on order characteristics
    if (order.totalAmount > 50000) return 'HIGH';
    if (order.items.some(item => item.storageRequirements?.includes('perishable'))) return 'HIGH';
    // No delivery method for agricultural marketplace
    return 'MEDIUM';
  }

  private generateSpecialInstructions(order: IOrder): string[] {
    const instructions: string[] = [];

    // Add instructions based on order characteristics
    if (order.items.some(item => item.storageRequirements?.includes('cold'))) {
      instructions.push('Maintain cold chain throughout fulfillment');
    }

    // No delivery method for agricultural marketplace

    if (order.buyerNotes) {
      instructions.push(`Customer notes: ${order.buyerNotes}`);
    }

    return instructions;
  }

  private async calculateResourceRequirements(
    order: IOrder, 
    stages: FulfillmentStage[]
  ): Promise<ResourceRequirement[]> {
    const requirements: ResourceRequirement[] = [];

    // Calculate based on stages and order characteristics
    if (stages.some(s => s.name === 'Harvesting')) {
      requirements.push({
        type: 'PERSONNEL',
        resource: 'Harvest Workers',
        quantity: Math.ceil(order.items.length / 2),
        duration: 48,
        availability: 'AVAILABLE'
      });
    }

    // No delivery method for agricultural marketplace
        quantity: 1,
        duration: 72,
        availability: 'AVAILABLE'
      });
    }

    return requirements;
  }

  private mapStageToOrderStatus(stageName: string): OrderStatus | null {
    const stageStatusMap: { [key: string]: OrderStatus } = {
      'Order Confirmation': OrderStatus.CONFIRMED,
      'Harvesting': OrderStatus.HARVESTING,
      'Quality Check': OrderStatus.QUALITY_CHECK,
      'Packaging': OrderStatus.PACKAGING,
      'Shipping': OrderStatus.SHIPPED
    };

    return stageStatusMap[stageName] || null;
  }

  private calculateOptimalHarvestDate(item: IOrderItem): Date {
    // Calculate optimal harvest date based on item characteristics
    const baseDate = new Date();
    baseDate.setDate(baseDate.getDate() + 7); // Default 7 days from now
    return baseDate;
  }

  private determineHarvestMethod(item: IOrderItem): 'MANUAL' | 'MECHANICAL' | 'HYBRID' {
    // Determine harvest method based on crop type and quantity
    if (item.quantity > 1000) return 'MECHANICAL';
    if (item.productSnapshot.category === 'Fruits') return 'MANUAL';
    return 'HYBRID';
  }

  private optimizeDeliveryMethod(order: IOrder): DeliveryMethod {
    // Optimize delivery method based on order characteristics
    if (order.items.some(item => item.storageRequirements?.includes('perishable'))) {
      return DeliveryMethod.COLD_CHAIN;
    }
    if (order.totalAmount > 10000) {
      return DeliveryMethod.EXPRESS_DELIVERY;
    }
    return DeliveryMethod.STANDARD_DELIVERY; // Default for agricultural marketplace
  }

  // Additional helper methods would be implemented here...
  private async checkStageDependencies(plan: FulfillmentPlan, stage: FulfillmentStage): Promise<boolean> {
    return stage.dependencies.every(depId => 
      plan.stages.find(s => s.id === depId)?.status === 'COMPLETED'
    );
  }

  private async executeStageLogic(orderId: string, stage: FulfillmentStage): Promise<void> {
    // Execute stage-specific logic
    console.log(`Executing stage ${stage.name} for order ${orderId}`);
  }

  private async startNextStage(plan: FulfillmentPlan): Promise<void> {
    // Find and start next available stage
    const nextStage = plan.stages.find(s => 
      s.status === 'PENDING' && 
      this.checkStageDependencies(plan, s)
    );
    
    if (nextStage) {
      await this.executeFulfillmentStage(plan.orderId, nextStage.id);
    }
  }

  private async completeFulfillment(orderId: string): Promise<void> {
    await this.updateOrderStatus(orderId, OrderStatus.COMPLETED);
  }

  // Placeholder methods for external service calls
  private async saveFulfillmentPlan(plan: FulfillmentPlan): Promise<void> {
    await axios.post(`${this.fulfillmentServiceUrl}/api/plans`, plan);
  }

  private async getFulfillmentPlan(orderId: string): Promise<FulfillmentPlan> {
    const response = await axios.get(`${this.fulfillmentServiceUrl}/api/plans/${orderId}`);
    return response.data;
  }

  private async updateFulfillmentPlan(plan: FulfillmentPlan): Promise<void> {
    await axios.put(`${this.fulfillmentServiceUrl}/api/plans/${plan.orderId}`, plan);
  }

  private async scheduleStages(plan: FulfillmentPlan): Promise<void> {
    console.log(`Scheduling stages for order ${plan.orderId}`);
  }

  private async processQualityChecks(orderId: string, checks: QualityCheck[], results: any): Promise<void> {
    console.log(`Processing quality checks for order ${orderId}`);
  }

  private async updateOrderStatus(orderId: string, status: OrderStatus): Promise<void> {
    console.log(`Updating order ${orderId} status to ${status}`);
  }

  private async saveHarvestSchedule(schedule: HarvestSchedule): Promise<void> {
    await axios.post(`${this.fulfillmentServiceUrl}/api/harvest-schedules`, schedule);
  }

  private async calculateDeliveryRoute(order: IOrder): Promise<any> {
    return { distance: 50, duration: 2 }; // Placeholder
  }

  private async assignDeliveryPartner(order: IOrder, method: DeliveryMethod): Promise<any> {
    return { name: 'AgriLogistics', id: 'AL001' }; // Placeholder
  }

  private async calculateDeliveryCost(order: IOrder, route: any, method: DeliveryMethod): Promise<number> {
    return 200; // Placeholder
  }

  private estimateDeliveryTime(order: IOrder, route: any, method: DeliveryMethod): Date {
    const hours = method === DeliveryMethod.EXPRESS_DELIVERY ? 24 : 72;
    return new Date(Date.now() + hours * 60 * 60 * 1000);
  }

  private getDeliverySpecialRequirements(order: IOrder): string[] {
    return order.items
      .filter(item => item.storageRequirements)
      .map(item => item.storageRequirements!)
      .flat();
  }

  private async saveDeliveryPlan(plan: any): Promise<void> {
    await axios.post(`${this.fulfillmentServiceUrl}/api/delivery-plans`, plan);
  }

  private async executeQualityCheck(orderId: string, itemId: string, check: QualityCheck): Promise<QualityCheck> {
    // Simulate quality check execution
    check.status = Math.random() > 0.1 ? 'PASSED' : 'FAILED';
    check.checkedAt = new Date();
    check.checkedBy = 'QA-System';
    return check;
  }

  private async saveQualityAssuranceResults(orderId: string, itemId: string, results: any): Promise<void> {
    await axios.post(`${this.fulfillmentServiceUrl}/api/quality-results`, {
      orderId,
      itemId,
      results
    });
  }

  private async handleQualityFailure(orderId: string, itemId: string, recommendations: string[]): Promise<void> {
    console.log(`Handling quality failure for order ${orderId}, item ${itemId}:`, recommendations);
  }
}

export default OrderFulfillmentService;
