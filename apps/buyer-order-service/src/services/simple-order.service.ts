import OrderModel, { IOrder, OrderStatus } from '../models/order.model';
import { AppError } from '../utils/error-handler';
import { logger } from '../utils/logger';

export class SimpleOrderService {
  constructor() {
    logger.info('SimpleOrderService initialized for agricultural marketplace');
  }

  async createOrder(orderData: any): Promise<IOrder> {
    try {
      logger.info('Creating new order for agricultural marketplace');
      
      // Basic order creation for agricultural marketplace
      const order = new OrderModel({
        ...orderData,
        status: OrderStatus.PENDING,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      const savedOrder = await order.save();
      logger.info(`Order created successfully: ${savedOrder._id}`);
      
      return savedOrder;
    } catch (error) {
      logger.error('Error creating order:', error);
      throw new AppError(500, 'Failed to create order');
    }
  }

  async getOrderById(orderId: string): Promise<IOrder | null> {
    try {
      const order = await OrderModel.findById(orderId);
      return order;
    } catch (error) {
      logger.error('Error fetching order:', error);
      throw new AppError(500, 'Failed to fetch order');
    }
  }

  async getOrdersByUserId(userId: string): Promise<IOrder[]> {
    try {
      const orders = await OrderModel.find({ userId }).sort({ createdAt: -1 });
      return orders;
    } catch (error) {
      logger.error('Error fetching user orders:', error);
      throw new AppError(500, 'Failed to fetch orders');
    }
  }

  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<IOrder | null> {
    try {
      const order = await OrderModel.findByIdAndUpdate(
        orderId,
        { status, updatedAt: new Date() },
        { new: true }
      );
      
      if (order) {
        logger.info(`Order ${orderId} status updated to ${status}`);
      }
      
      return order;
    } catch (error) {
      logger.error('Error updating order status:', error);
      throw new AppError(500, 'Failed to update order status');
    }
  }

  async getAllOrders(page: number = 1, limit: number = 10): Promise<{ orders: IOrder[], total: number }> {
    try {
      const skip = (page - 1) * limit;
      const orders = await OrderModel.find()
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await OrderModel.countDocuments();

      return { orders, total };
    } catch (error) {
      logger.error('Error fetching all orders:', error);
      throw new AppError(500, 'Failed to fetch orders');
    }
  }

  async cancelOrder(orderId: string, userId: string): Promise<IOrder | null> {
    try {
      // Find order and verify ownership
      const order = await OrderModel.findOne({ _id: orderId, userId });
      if (!order) {
        throw new AppError(404, 'Order not found or unauthorized');
      }

      // Check if order can be cancelled
      if (order.status === OrderStatus.CANCELLED || order.status === OrderStatus.DELIVERED) {
        throw new AppError(400, 'Order cannot be cancelled');
      }

      // Update order status to cancelled
      const cancelledOrder = await OrderModel.findByIdAndUpdate(
        orderId,
        { status: OrderStatus.CANCELLED, updatedAt: new Date() },
        { new: true }
      );

      logger.info(`Order cancelled successfully: ${orderId}`);
      return cancelledOrder;
    } catch (error) {
      logger.error('Error cancelling order:', error);
      if (error instanceof AppError) throw error;
      throw new AppError(500, 'Failed to cancel order');
    }
  }
}
