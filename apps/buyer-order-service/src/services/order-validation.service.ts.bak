import { IOrder, <PERSON><PERSON>rderI<PERSON>, OrderStatus, ProductType } from '../models/order.model';
import { ProductService } from './product.service';
import { InventoryService } from './inventory.service';
import { AppError } from '../utils/error-handler';

/**
 * Validation rule interface
 */
export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  type: 'BUSINESS' | 'COMPLIANCE' | 'SEASONAL' | 'GEOGRAPHIC' | 'INVENTORY' | 'PRICING';
  severity: 'ERROR' | 'WARNING' | 'INFO';
  isActive: boolean;
  conditions: ValidationCondition[];
  action: ValidationAction;
}

/**
 * Validation condition interface
 */
export interface ValidationCondition {
  field: string;
  operator: 'EQUALS' | 'NOT_EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'CONTAINS' | 'IN' | 'NOT_IN';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

/**
 * Validation action interface
 */
export interface ValidationAction {
  type: 'BLOCK' | 'WARN' | 'MODIFY' | 'REQUIRE_APPROVAL';
  message: string;
  suggestedFix?: string;
  autoFix?: boolean;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: ValidationSuggestion[];
  autoFixesApplied: string[];
}

/**
 * Validation error interface
 */
export interface ValidationError {
  ruleId: string;
  field: string;
  message: string;
  severity: 'ERROR' | 'WARNING';
  suggestedFix?: string;
}

/**
 * Validation warning interface
 */
export interface ValidationWarning {
  ruleId: string;
  field: string;
  message: string;
  impact: 'LOW' | 'MEDIUM' | 'HIGH';
}

/**
 * Validation suggestion interface
 */
export interface ValidationSuggestion {
  type: 'ALTERNATIVE_PRODUCT' | 'BETTER_DELIVERY' | 'DISCOUNT_OPPORTUNITY' | 'BULK_DISCOUNT';
  message: string;
  data?: any;
}

/**
 * Order validation service for comprehensive business rule validation
 */
export class OrderValidationService {
  private productService: ProductService;
  private inventoryService: InventoryService;
  private validationRules: Map<string, ValidationRule>;

  constructor() {
    this.productService = new ProductService();
    this.inventoryService = new InventoryService();
    this.validationRules = new Map();
    this.loadValidationRules();
  }

  /**
   * Validate complete order
   */
  async validateOrder(order: Partial<IOrder>): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];
    const autoFixesApplied: string[] = [];

    try {
      // Run all validation rules
      for (const rule of this.validationRules.values()) {
        if (!rule.isActive) continue;

        const ruleResult = await this.executeValidationRule(rule, order);
        
        if (ruleResult.violated) {
          if (rule.severity === 'ERROR') {
            errors.push({
              ruleId: rule.id,
              field: ruleResult.field,
              message: rule.action.message,
              severity: 'ERROR',
              suggestedFix: rule.action.suggestedFix
            });
          } else if (rule.severity === 'WARNING') {
            warnings.push({
              ruleId: rule.id,
              field: ruleResult.field,
              message: rule.action.message,
              impact: 'MEDIUM'
            });
          }

          // Apply auto-fix if available
          if (rule.action.autoFix && rule.action.type === 'MODIFY') {
            const fixResult = await this.applyAutoFix(rule, order, ruleResult);
            if (fixResult.success) {
              autoFixesApplied.push(fixResult.description);
            }
          }
        }
      }

      // Generate suggestions
      const orderSuggestions = await this.generateSuggestions(order);
      suggestions.push(...orderSuggestions);

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions,
        autoFixesApplied
      };

    } catch (error) {
      throw new AppError(500, `Order validation failed: ${error.message}`);
    }
  }

  /**
   * Validate order items
   */
  async validateOrderItems(items: IOrderItem[]): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    for (const item of items) {
      // Validate product availability
      const availabilityResult = await this.validateProductAvailability(item);
      if (!availabilityResult.isValid) {
        errors.push(...availabilityResult.errors);
        warnings.push(...availabilityResult.warnings);
      }

      // Validate pricing
      const pricingResult = await this.validateItemPricing(item);
      if (!pricingResult.isValid) {
        errors.push(...pricingResult.errors);
        warnings.push(...pricingResult.warnings);
      }

      // Validate seasonal restrictions
      const seasonalResult = await this.validateSeasonalRestrictions(item);
      if (!seasonalResult.isValid) {
        warnings.push(...seasonalResult.warnings);
      }

      // Generate item-specific suggestions
      const itemSuggestions = await this.generateItemSuggestions(item);
      suggestions.push(...itemSuggestions);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      autoFixesApplied: []
    };
  }

  /**
   * Validate user eligibility for order
   */
  async validateUserEligibility(userId: string, order: Partial<IOrder>): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    try {
      // Check user verification status
      const userVerification = await this.checkUserVerification(userId);
      if (!userVerification.isVerified) {
        errors.push({
          ruleId: 'USER_VERIFICATION',
          field: 'userId',
          message: 'User verification required for agricultural product orders',
          severity: 'ERROR',
          suggestedFix: 'Complete user verification process'
        });
      }

      // Check order limits
      const orderLimits = await this.checkOrderLimits(userId, order);
      if (!orderLimits.isValid) {
        errors.push(...orderLimits.errors);
        warnings.push(...orderLimits.warnings);
      }

      // Check geographic restrictions
      const geoRestrictions = await this.checkGeographicRestrictions(userId, order);
      if (!geoRestrictions.isValid) {
        errors.push(...geoRestrictions.errors);
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions: [],
        autoFixesApplied: []
      };

    } catch (error) {
      throw new AppError(500, `User eligibility validation failed: ${error.message}`);
    }
  }

  /**
   * Validate compliance requirements
   */
  async validateCompliance(order: Partial<IOrder>): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check agricultural compliance
    const agriCompliance = await this.validateAgriculturalCompliance(order);
    if (!agriCompliance.isValid) {
      errors.push(...agriCompliance.errors);
      warnings.push(...agriCompliance.warnings);
    }

    // Check export/import restrictions
    const tradeCompliance = await this.validateTradeCompliance(order);
    if (!tradeCompliance.isValid) {
      errors.push(...tradeCompliance.errors);
    }

    // Check quality standards
    const qualityCompliance = await this.validateQualityStandards(order);
    if (!qualityCompliance.isValid) {
      warnings.push(...qualityCompliance.warnings);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions: [],
      autoFixesApplied: []
    };
  }

  /**
   * Private validation methods
   */
  private async executeValidationRule(
    rule: ValidationRule, 
    order: Partial<IOrder>
  ): Promise<{ violated: boolean; field: string; value?: any }> {
    let violated = false;
    let field = '';
    let value: any;

    for (const condition of rule.conditions) {
      const fieldValue = this.getFieldValue(order, condition.field);
      const conditionMet = this.evaluateCondition(fieldValue, condition);

      if (condition.logicalOperator === 'OR') {
        violated = violated || conditionMet;
      } else {
        violated = violated && conditionMet;
      }

      if (conditionMet) {
        field = condition.field;
        value = fieldValue;
      }
    }

    return { violated, field, value };
  }

  private evaluateCondition(fieldValue: any, condition: ValidationCondition): boolean {
    switch (condition.operator) {
      case 'EQUALS':
        return fieldValue === condition.value;
      case 'NOT_EQUALS':
        return fieldValue !== condition.value;
      case 'GREATER_THAN':
        return fieldValue > condition.value;
      case 'LESS_THAN':
        return fieldValue < condition.value;
      case 'CONTAINS':
        return Array.isArray(fieldValue) ? 
          fieldValue.includes(condition.value) : 
          String(fieldValue).includes(condition.value);
      case 'IN':
        return Array.isArray(condition.value) ? 
          condition.value.includes(fieldValue) : false;
      case 'NOT_IN':
        return Array.isArray(condition.value) ? 
          !condition.value.includes(fieldValue) : true;
      default:
        return false;
    }
  }

  private getFieldValue(order: Partial<IOrder>, fieldPath: string): any {
    const fields = fieldPath.split('.');
    let value: any = order;
    
    for (const field of fields) {
      value = value?.[field];
    }
    
    return value;
  }

  private async applyAutoFix(
    rule: ValidationRule, 
    order: Partial<IOrder>, 
    ruleResult: any
  ): Promise<{ success: boolean; description: string }> {
    // Implement auto-fix logic based on rule type
    switch (rule.id) {
      case 'MINIMUM_ORDER_VALUE':
        // Auto-suggest products to meet minimum order value
        return { success: true, description: 'Suggested additional products to meet minimum order value' };
      
      case 'DELIVERY_DATE_ADJUSTMENT':
        // Auto-adjust delivery date based on product availability
        return { success: true, description: 'Adjusted delivery date based on product availability' };
      
      default:
        return { success: false, description: 'No auto-fix available' };
    }
  }

  private async validateProductAvailability(item: IOrderItem): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    try {
      const availability = await this.inventoryService.checkAvailability(
        item.productId.toString(),
        item.quantity
      );

      if (!availability.isAvailable) {
        errors.push({
          ruleId: 'PRODUCT_AVAILABILITY',
          field: 'quantity',
          message: `Only ${availability.availableQuantity} units available for ${item.productSnapshot.name}`,
          severity: 'ERROR',
          suggestedFix: `Reduce quantity to ${availability.availableQuantity} or choose alternative product`
        });
      }

      return { isValid: errors.length === 0, errors, warnings, suggestions: [], autoFixesApplied: [] };
    } catch (error) {
      errors.push({
        ruleId: 'AVAILABILITY_CHECK_FAILED',
        field: 'productId',
        message: 'Unable to verify product availability',
        severity: 'ERROR'
      });
      return { isValid: false, errors, warnings, suggestions: [], autoFixesApplied: [] };
    }
  }

  private async validateItemPricing(item: IOrderItem): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    try {
      const currentPrice = await this.productService.getCurrentPrice(item.productId.toString());
      const priceVariance = Math.abs(currentPrice - item.unitPrice) / currentPrice;

      if (priceVariance > 0.1) { // 10% variance
        errors.push({
          ruleId: 'PRICE_VARIANCE',
          field: 'unitPrice',
          message: `Price has changed significantly. Current: ₹${currentPrice}, Order: ₹${item.unitPrice}`,
          severity: 'ERROR',
          suggestedFix: `Update price to current rate of ₹${currentPrice}`
        });
      } else if (priceVariance > 0.05) { // 5% variance
        warnings.push({
          ruleId: 'PRICE_CHANGE',
          field: 'unitPrice',
          message: `Price has changed slightly. Current: ₹${currentPrice}, Order: ₹${item.unitPrice}`,
          impact: 'LOW'
        });
      }

      return { isValid: errors.length === 0, errors, warnings, suggestions: [], autoFixesApplied: [] };
    } catch (error) {
      return { isValid: true, errors: [], warnings: [], suggestions: [], autoFixesApplied: [] };
    }
  }

  private async validateSeasonalRestrictions(item: IOrderItem): Promise<ValidationResult> {
    const warnings: ValidationWarning[] = [];

    // Check if crop is in season
    if (item.productType === ProductType.CROP) {
      const currentMonth = new Date().getMonth() + 1;
      const seasonalRestrictions = this.getSeasonalRestrictions(item.productSnapshot.category);

      if (seasonalRestrictions && !seasonalRestrictions.includes(currentMonth)) {
        warnings.push({
          ruleId: 'SEASONAL_RESTRICTION',
          field: 'productId',
          message: `${item.productSnapshot.name} is not typically available in current season`,
          impact: 'MEDIUM'
        });
      }
    }

    return { isValid: true, errors: [], warnings, suggestions: [], autoFixesApplied: [] };
  }

  private async generateSuggestions(order: Partial<IOrder>): Promise<ValidationSuggestion[]> {
    const suggestions: ValidationSuggestion[] = [];

    // Bulk discount suggestions
    if (order.totalAmount && order.totalAmount > 10000) {
      suggestions.push({
        type: 'BULK_DISCOUNT',
        message: 'You may be eligible for bulk discount on orders above ₹15,000',
        data: { threshold: 15000, discount: 5 }
      });
    }

    // No delivery suggestions for agricultural marketplace
        data: { upgradeOption: 'EXPRESS_DELIVERY', cost: 0 }
      });
    }

    return suggestions;
  }

  private async generateItemSuggestions(item: IOrderItem): Promise<ValidationSuggestion[]> {
    const suggestions: ValidationSuggestion[] = [];

    // Alternative product suggestions
    if (item.productType === ProductType.CROP) {
      suggestions.push({
        type: 'ALTERNATIVE_PRODUCT',
        message: `Similar ${item.productSnapshot.category} products available with better ratings`,
        data: { category: item.productSnapshot.category }
      });
    }

    return suggestions;
  }

  // Additional validation helper methods...
  private async checkUserVerification(userId: string): Promise<{ isVerified: boolean }> {
    // Check user verification status
    return { isVerified: true }; // Placeholder
  }

  private async checkOrderLimits(userId: string, order: Partial<IOrder>): Promise<ValidationResult> {
    // Check user order limits
    return { isValid: true, errors: [], warnings: [], suggestions: [], autoFixesApplied: [] };
  }

  private async checkGeographicRestrictions(userId: string, order: Partial<IOrder>): Promise<ValidationResult> {
    // Check geographic restrictions
    return { isValid: true, errors: [], warnings: [], suggestions: [], autoFixesApplied: [] };
  }

  private async validateAgriculturalCompliance(order: Partial<IOrder>): Promise<ValidationResult> {
    // Validate agricultural compliance requirements
    return { isValid: true, errors: [], warnings: [], suggestions: [], autoFixesApplied: [] };
  }

  private async validateTradeCompliance(order: Partial<IOrder>): Promise<ValidationResult> {
    // Validate trade compliance
    return { isValid: true, errors: [], warnings: [], suggestions: [], autoFixesApplied: [] };
  }

  private async validateQualityStandards(order: Partial<IOrder>): Promise<ValidationResult> {
    // Validate quality standards
    return { isValid: true, errors: [], warnings: [], suggestions: [], autoFixesApplied: [] };
  }

  private getSeasonalRestrictions(category: string): number[] | null {
    const seasonalMap: { [key: string]: number[] } = {
      'Rice': [6, 7, 8, 9, 10], // Monsoon season
      'Wheat': [11, 12, 1, 2, 3], // Winter season
      'Mango': [3, 4, 5, 6], // Summer season
      'Apple': [9, 10, 11, 12] // Post-monsoon
    };

    return seasonalMap[category] || null;
  }

  private loadValidationRules(): void {
    const rules: ValidationRule[] = [
      {
        id: 'MINIMUM_ORDER_VALUE',
        name: 'Minimum Order Value',
        description: 'Order must meet minimum value requirement',
        type: 'BUSINESS',
        severity: 'ERROR',
        isActive: true,
        conditions: [
          { field: 'totalAmount', operator: 'LESS_THAN', value: 500 }
        ],
        action: {
          type: 'BLOCK',
          message: 'Minimum order value is ₹500',
          suggestedFix: 'Add more items to reach minimum order value',
          autoFix: true
        }
      },
      {
        id: 'PERISHABLE_DELIVERY_TIME',
        name: 'Perishable Product Delivery',
        description: 'Perishable products must be delivered within 48 hours',
        type: 'BUSINESS',
        severity: 'WARNING',
        isActive: true,
        conditions: [
          { field: 'items.storageRequirements', operator: 'CONTAINS', value: 'perishable' }
        ],
        action: {
          type: 'WARN',
          message: 'Perishable products should be delivered within 48 hours',
          suggestedFix: 'Choose express delivery for perishable items'
        }
      },
      {
        id: 'BULK_ORDER_DISCOUNT',
        name: 'Bulk Order Discount Eligibility',
        description: 'Orders above certain value eligible for bulk discount',
        type: 'BUSINESS',
        severity: 'INFO',
        isActive: true,
        conditions: [
          { field: 'totalAmount', operator: 'GREATER_THAN', value: 15000 }
        ],
        action: {
          type: 'MODIFY',
          message: 'Bulk discount of 5% applicable',
          autoFix: true
        }
      }
    ];

    rules.forEach(rule => {
      this.validationRules.set(rule.id, rule);
    });
  }
}

export default OrderValidationService;
