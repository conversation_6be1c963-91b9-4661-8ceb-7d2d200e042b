import { IOrder, OrderStatus } from '../models/order.model';
import { NotificationService, NotificationType } from './notification.service';
import { AppError } from '../utils/error-handler';
import axios from 'axios';

/**
 * Tracking event interface
 */
export interface TrackingEvent {
  id: string;
  orderId: string;
  status: OrderStatus;
  timestamp: Date;
  location?: {
    latitude: number;
    longitude: number;
    address: string;
    city: string;
    state: string;
  };
  description: string;
  metadata?: any;
  source: 'SYSTEM' | 'MANUAL' | 'EXTERNAL' | 'SENSOR';
}

/**
 * Delivery update interface
 */
export interface DeliveryUpdate {
  trackingNumber: string;
  status: string;
  location: string;
  timestamp: Date;
  estimatedDelivery?: Date;
  deliveryPartner?: string;
  contactNumber?: string;
}

/**
 * Real-time tracking data interface
 */
export interface RealTimeTrackingData {
  orderId: string;
  currentStatus: OrderStatus;
  currentLocation?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  estimatedDeliveryTime: Date;
  deliveryProgress: number; // 0-100 percentage
  nextMilestone: {
    status: OrderStatus;
    estimatedTime: Date;
    description: string;
  };
  events: TrackingEvent[];
  deliveryPartner?: {
    name: string;
    phone: string;
    vehicleNumber?: string;
  };
}

/**
 * Order tracking service for real-time order monitoring and updates
 */
export class OrderTrackingService {
  private notificationService: NotificationService;
  private trackingServiceUrl: string;
  private deliveryPartnerApis: Map<string, string>;

  constructor() {
    this.notificationService = new NotificationService();
    this.trackingServiceUrl = process.env.TRACKING_SERVICE_URL || 'http://localhost:3007';
    this.deliveryPartnerApis = new Map();
    this.loadDeliveryPartnerConfigs();
  }

  /**
   * Initialize tracking for an order
   */
  async initializeOrderTracking(order: IOrder): Promise<void> {
    try {
      const initialEvent: TrackingEvent = {
        id: this.generateEventId(),
        orderId: order.id,
        status: order.status,
        timestamp: new Date(),
        description: 'Order created and tracking initialized',
        source: 'SYSTEM'
      };

      await this.addTrackingEvent(initialEvent);
      
      // Set up automated tracking based on order type
      await this.setupAutomatedTracking(order);

    } catch (error) {
      console.error('Failed to initialize order tracking:', error);
      throw new AppError(500, `Failed to initialize tracking: ${error.message}`);
    }
  }

  /**
   * Add tracking event
   */
  async addTrackingEvent(event: TrackingEvent): Promise<void> {
    try {
      await axios.post(`${this.trackingServiceUrl}/api/tracking/events`, event);
      
      // Send real-time update to user
      await this.sendRealTimeUpdate(event.orderId, event);
      
      // Check if notification should be sent
      if (this.shouldSendNotification(event.status)) {
        await this.notificationService.sendOrderStatusUpdateNotification(
          await this.getOrderById(event.orderId)
        );
      }

    } catch (error) {
      console.error('Failed to add tracking event:', error);
      throw new AppError(500, `Failed to add tracking event: ${error.message}`);
    }
  }

  /**
   * Get real-time tracking data for an order
   */
  async getRealTimeTrackingData(orderId: string): Promise<RealTimeTrackingData> {
    try {
      const response = await axios.get(
        `${this.trackingServiceUrl}/api/tracking/orders/${orderId}/realtime`
      );
      return response.data;
    } catch (error) {
      throw new AppError(500, `Failed to get tracking data: ${error.message}`);
    }
  }

  /**
   * Update delivery status from external partner
   */
  async updateDeliveryStatus(update: DeliveryUpdate): Promise<void> {
    try {
      // Find order by tracking number
      const order = await this.getOrderByTrackingNumber(update.trackingNumber);
      if (!order) {
        throw new AppError(404, 'Order not found for tracking number');
      }

      // Map external status to internal status
      const internalStatus = this.mapExternalStatus(update.status);
      
      const trackingEvent: TrackingEvent = {
        id: this.generateEventId(),
        orderId: order.id,
        status: internalStatus,
        timestamp: update.timestamp,
        location: this.parseLocation(update.location),
        description: `Delivery update: ${update.status}`,
        source: 'EXTERNAL',
        metadata: {
          deliveryPartner: update.deliveryPartner,
          contactNumber: update.contactNumber,
          originalStatus: update.status
        }
      };

      await this.addTrackingEvent(trackingEvent);

      // Update estimated delivery if provided
      if (update.estimatedDelivery) {
        await this.updateEstimatedDelivery(order.id, update.estimatedDelivery);
      }

    } catch (error) {
      console.error('Failed to update delivery status:', error);
      throw new AppError(500, `Failed to update delivery status: ${error.message}`);
    }
  }

  /**
   * Track order with external delivery partner
   */
  async trackWithDeliveryPartner(
    trackingNumber: string, 
    partnerName: string
  ): Promise<DeliveryUpdate[]> {
    try {
      const partnerApiUrl = this.deliveryPartnerApis.get(partnerName);
      if (!partnerApiUrl) {
        throw new AppError(400, `Unsupported delivery partner: ${partnerName}`);
      }

      const response = await axios.get(
        `${partnerApiUrl}/track/${trackingNumber}`,
        {
          headers: {
            'Authorization': `Bearer ${process.env[`${partnerName.toUpperCase()}_API_KEY`]}`
          }
        }
      );

      return this.parsePartnerResponse(partnerName, response.data);

    } catch (error) {
      console.error(`Failed to track with ${partnerName}:`, error);
      throw new AppError(500, `Failed to track with delivery partner: ${error.message}`);
    }
  }

  /**
   * Get order tracking history
   */
  async getOrderTrackingHistory(orderId: string): Promise<TrackingEvent[]> {
    try {
      const response = await axios.get(
        `${this.trackingServiceUrl}/api/tracking/orders/${orderId}/history`
      );
      return response.data;
    } catch (error) {
      throw new AppError(500, `Failed to get tracking history: ${error.message}`);
    }
  }

  /**
   * Predict delivery time based on current status and location
   */
  async predictDeliveryTime(orderId: string): Promise<{
    estimatedDelivery: Date;
    confidence: number;
    factors: string[];
  }> {
    try {
      const trackingData = await this.getRealTimeTrackingData(orderId);
      const order = await this.getOrderById(orderId);

      const factors: string[] = [];
      let estimatedHours = 0;

      // Base time based on current status
      const statusTimeMap: { [key in OrderStatus]?: number } = {
        [OrderStatus.PROCESSING]: 48,
        [OrderStatus.HARVESTING]: 24,
        [OrderStatus.PACKAGING]: 12,
        [OrderStatus.SHIPPED]: 72,
        [OrderStatus.IN_TRANSIT]: 24
      };

      estimatedHours = statusTimeMap[trackingData.currentStatus] || 24;
      factors.push(`Base time for ${trackingData.currentStatus}: ${estimatedHours}h`);

      // No shipping address for agricultural marketplace
        factors.push(`Distance adjustment: +${distanceHours.toFixed(1)}h`);
      }

      // Adjust for product type
      const hasPerishableItems = order.items.some(item => 
        item.storageRequirements?.includes('perishable')
      );
      if (hasPerishableItems) {
        estimatedHours *= 0.8; // Faster for perishables
        factors.push('Perishable items: -20% time');
      }

      // Weather and traffic factors (simplified)
      const weatherDelay = Math.random() * 6; // 0-6 hours random delay
      estimatedHours += weatherDelay;
      factors.push(`Weather/traffic: +${weatherDelay.toFixed(1)}h`);

      const estimatedDelivery = new Date(Date.now() + estimatedHours * 60 * 60 * 1000);
      const confidence = Math.max(0.6, 1 - (weatherDelay / 12)); // Lower confidence with more uncertainty

      return {
        estimatedDelivery,
        confidence,
        factors
      };

    } catch (error) {
      throw new AppError(500, `Failed to predict delivery time: ${error.message}`);
    }
  }

  /**
   * Set up delivery reminders
   */
  async setupDeliveryReminders(orderId: string): Promise<void> {
    try {
      const prediction = await this.predictDeliveryTime(orderId);
      
      // Schedule reminder 2 hours before estimated delivery
      const reminderTime = new Date(prediction.estimatedDelivery.getTime() - 2 * 60 * 60 * 1000);
      
      if (reminderTime > new Date()) {
        await this.scheduleDeliveryReminder(orderId, reminderTime);
      }

      // Schedule day-of reminder
      const dayOfReminder = new Date(prediction.estimatedDelivery);
      dayOfReminder.setHours(9, 0, 0, 0); // 9 AM on delivery day
      
      if (dayOfReminder > new Date()) {
        await this.scheduleDeliveryReminder(orderId, dayOfReminder);
      }

    } catch (error) {
      console.error('Failed to setup delivery reminders:', error);
    }
  }

  /**
   * Handle delivery exceptions
   */
  async handleDeliveryException(
    orderId: string, 
    exceptionType: string, 
    description: string
  ): Promise<void> {
    try {
      const exceptionEvent: TrackingEvent = {
        id: this.generateEventId(),
        orderId,
        status: OrderStatus.IN_TRANSIT, // Keep current status
        timestamp: new Date(),
        description: `Delivery exception: ${description}`,
        source: 'SYSTEM',
        metadata: {
          exceptionType,
          requiresAction: true
        }
      };

      await this.addTrackingEvent(exceptionEvent);

      // Send immediate notification for exceptions
      const order = await this.getOrderById(orderId);
      await this.notificationService.sendCustomNotification(
        order.userId.toString(),
        NotificationType.PUSH,
        'Delivery Update',
        `Issue with your order ${order.orderNumber}: ${description}`,
        'HIGH'
      );

      // Create support ticket if needed
      if (this.requiresSupportTicket(exceptionType)) {
        await this.createSupportTicket(orderId, exceptionType, description);
      }

    } catch (error) {
      console.error('Failed to handle delivery exception:', error);
    }
  }

  /**
   * Private helper methods
   */
  private async setupAutomatedTracking(order: IOrder): Promise<void> {
    // Set up automated tracking based on order characteristics
    if (order.items.some(item => item.productType === 'CROP')) {
      await this.setupCropTracking(order);
    }
    
    // No delivery method for agricultural marketplace
  }

  private async setupCropTracking(order: IOrder): Promise<void> {
    // Set up tracking for crop-specific milestones
    const milestones = ['HARVESTING', 'QUALITY_CHECK', 'PACKAGING'];
    
    for (const milestone of milestones) {
      // Schedule automated checks for these milestones
      console.log(`Setting up tracking for ${milestone} milestone`);
    }
  }

  private async setupTemperatureTracking(order: IOrder): Promise<void> {
    // Set up temperature monitoring for cold chain delivery
    console.log('Setting up temperature tracking for cold chain delivery');
  }

  private shouldSendNotification(status: OrderStatus): boolean {
    const notificationStatuses = [
      OrderStatus.CONFIRMED,
      OrderStatus.PAID,
      OrderStatus.SHIPPED,
      OrderStatus.DELIVERED,
      OrderStatus.CANCELLED
    ];
    return notificationStatuses.includes(status);
  }

  private async sendRealTimeUpdate(orderId: string, event: TrackingEvent): Promise<void> {
    // Send real-time update via WebSocket or Server-Sent Events
    try {
      await axios.post(`${this.trackingServiceUrl}/api/tracking/realtime-update`, {
        orderId,
        event
      });
    } catch (error) {
      console.error('Failed to send real-time update:', error);
    }
  }

  private mapExternalStatus(externalStatus: string): OrderStatus {
    const statusMap: { [key: string]: OrderStatus } = {
      'picked_up': OrderStatus.SHIPPED,
      'in_transit': OrderStatus.IN_TRANSIT,
      'out_for_delivery': OrderStatus.IN_TRANSIT,
      'delivered': OrderStatus.DELIVERED,
      'failed_delivery': OrderStatus.IN_TRANSIT,
      'returned': OrderStatus.CANCELLED
    };

    return statusMap[externalStatus.toLowerCase()] || OrderStatus.IN_TRANSIT;
  }

  private parseLocation(locationString: string): any {
    // Parse location string to coordinates and address
    // This is a simplified implementation
    return {
      address: locationString,
      city: 'Unknown',
      state: 'Unknown'
    };
  }

  private parsePartnerResponse(partnerName: string, response: any): DeliveryUpdate[] {
    // Parse response from different delivery partners
    // This would be implemented based on each partner's API format
    return [];
  }

  private calculateDistance(from: any, to: any): number {
    // Calculate distance between two coordinates
    // Simplified implementation
    return 50; // Default 50km
  }

  private async scheduleDeliveryReminder(orderId: string, reminderTime: Date): Promise<void> {
    const order = await this.getOrderById(orderId);
    await this.notificationService.scheduleNotification({
      userId: order.userId.toString(),
      type: NotificationType.PUSH,
      templateId: 'DELIVERY_REMINDER_PUSH',
      variables: {
        orderNumber: order.orderNumber,
        estimatedTime: reminderTime.toLocaleString()
      },
      priority: 'MEDIUM'
    }, reminderTime);
  }

  private requiresSupportTicket(exceptionType: string): boolean {
    const criticalExceptions = ['DAMAGED_PACKAGE', 'LOST_PACKAGE', 'DELIVERY_REFUSED'];
    return criticalExceptions.includes(exceptionType);
  }

  private async createSupportTicket(orderId: string, type: string, description: string): Promise<void> {
    // Create support ticket for delivery issues
    console.log(`Creating support ticket for order ${orderId}: ${type} - ${description}`);
  }

  private generateEventId(): string {
    return `TRK-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
  }

  private async getOrderById(orderId: string): Promise<IOrder> {
    // This would typically fetch from the order service
    // Placeholder implementation
    throw new Error('Method not implemented');
  }

  private async getOrderByTrackingNumber(trackingNumber: string): Promise<IOrder | null> {
    // This would typically fetch from the order service
    // Placeholder implementation
    return null;
  }

  private async updateEstimatedDelivery(orderId: string, estimatedDelivery: Date): Promise<void> {
    // Update estimated delivery date in order
    console.log(`Updating estimated delivery for order ${orderId} to ${estimatedDelivery}`);
  }

  private loadDeliveryPartnerConfigs(): void {
    this.deliveryPartnerApis.set('DELHIVERY', 'https://track.delhivery.com/api');
    this.deliveryPartnerApis.set('BLUEDART', 'https://api.bluedart.com');
    this.deliveryPartnerApis.set('DTDC', 'https://api.dtdc.com');
  }
}

export default OrderTrackingService;
