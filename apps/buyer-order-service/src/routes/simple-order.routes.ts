import { Router } from 'express';
import { SimpleOrderService } from '../services/simple-order.service';
import { logger } from '../utils/logger';

const router = Router();
const orderService = new SimpleOrderService();

// Health check
router.get('/health', (req, res) => {
  res.json({ status: 'OK', service: 'buyer-order-service' });
});

// Get all orders
router.get('/orders', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    
    const result = await orderService.getAllOrders(page, limit);
    
    res.json({
      success: true,
      data: result.orders,
      pagination: {
        page,
        limit,
        total: result.total,
        pages: Math.ceil(result.total / limit)
      }
    });
  } catch (error) {
    logger.error('Error in GET /orders:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch orders'
    });
  }
});

// Get order by ID
router.get('/orders/:id', async (req, res) => {
  try {
    const order = await orderService.getOrderById(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }
    
    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    logger.error('Error in GET /orders/:id:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch order'
    });
  }
});

// Get orders by user ID
router.get('/users/:userId/orders', async (req, res) => {
  try {
    const orders = await orderService.getOrdersByUserId(req.params.userId);
    
    res.json({
      success: true,
      data: orders
    });
  } catch (error) {
    logger.error('Error in GET /users/:userId/orders:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user orders'
    });
  }
});

// Create new order
router.post('/orders', async (req, res) => {
  try {
    const order = await orderService.createOrder(req.body);
    
    res.status(201).json({
      success: true,
      data: order,
      message: 'Order created successfully'
    });
  } catch (error) {
    logger.error('Error in POST /orders:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create order'
    });
  }
});

// Update order status
router.patch('/orders/:id/status', async (req, res) => {
  try {
    const { status } = req.body;
    const order = await orderService.updateOrderStatus(req.params.id, status);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }
    
    res.json({
      success: true,
      data: order,
      message: 'Order status updated successfully'
    });
  } catch (error) {
    logger.error('Error in PATCH /orders/:id/status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update order status'
    });
  }
});

export default router;
