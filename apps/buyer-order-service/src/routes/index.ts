import { Application } from 'express';
import simpleOrderRoutes from './simple-order.routes';

export function setupRoutes(app: Application): void {
  // Mount routes
  app.use('/api', simpleOrderRoutes);

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'ok',
      service: 'buyer-order-service',
      timestamp: new Date().toISOString()
    });
  });

  // Root endpoint
  app.get('/api', (req, res) => {
    res.json({
      message: 'Welcome to Buyer Order Service!',
      version: '1.0.0',
      service: 'buyer-order-service',
      endpoints: {
        health: '/health',
        orders: '/api/orders',
        createOrder: 'POST /api/orders',
        getOrder: 'GET /api/orders/:orderId',
        getUserOrders: 'GET /api/orders/user/me',
        updateOrderStatus: 'PATCH /api/orders/:orderId/status',
        cancelOrder: 'POST /api/orders/:orderId/cancel'
      },
      timestamp: new Date().toISOString()
    });
  });
}

export { default as orderRoutes } from './order.routes';