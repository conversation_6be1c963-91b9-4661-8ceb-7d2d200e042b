import { jwtDecode } from "jwt-decode";

export interface DecodedToken {
  id?: string | number;
  farmer_id?: string;
  exp?: number;
  name?: string;
  email?: string;
  mobile_number?: string;
  role?: {
    code: string;
    name: string;
  };
}

export interface Environment {
  mode: 'development' | 'production';
  token: string;
  tokenData: DecodedToken;

  apiUrl: string;
  
  authApiUrl: string;
  productApiUrl: string;
  orderApiUrl: string;
  cartApiUrl: string;
  searchApiUrl: string;
  profileApiUrl: string;
}



const decoded_token: DecodedToken = (() => {
  const token = localStorage.getItem("token");
  if (!token) return {};
  try {
    const decodedToken:any = jwtDecode(token);
    return {
      ...decodedToken,
      userType: decodedToken?.roles?.code,
    };
  } catch (error) {
    console.warn('Failed to decode JWT token:', error);
    return {};
  }
})();

export const environment: Environment = {
  mode: import.meta.env.MODE as 'development' | 'production',
  apiUrl: import.meta.env.VITE_API_URL,
  token: localStorage.getItem("token") || '',
  tokenData: decoded_token,

  authApiUrl: import.meta.env.VITE_AUTH_API_URL || '',
  productApiUrl: import.meta.env.VITE_PRODUCT_API_URL || '',
  orderApiUrl: import.meta.env.VITE_ORDER_API_URL || '',
  cartApiUrl: import.meta.env.VITE_CART_API_URL || '',
  searchApiUrl: import.meta.env.VITE_SEARCH_API_URL || '',
  profileApiUrl: import.meta.env.VITE_PROFILE_API_URL || '',
}; 