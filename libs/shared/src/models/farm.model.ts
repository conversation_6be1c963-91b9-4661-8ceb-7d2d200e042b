import mongoose, { Schema, Document } from 'mongoose';

/**
 * Farm document interface
 */
export interface FarmDocument extends Document {
  farmId: string;
  farmerId: string; // References the farmerId in the Farmer model
  name: string; // Farm name for better identification
  location: {
    country: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications: string[];
  status: 'ACTIVE' | 'INACTIVE';
  plots: mongoose.Types.ObjectId[];

  // Enhanced farm management fields
  currentCrops: [{
    cropId: string;
    plotId: string;
    name: string;
    variety: string;
    plantingDate: Date;
    expectedHarvestDate: Date;
    growthStage: string;
    season: 'KHARIF' | 'RABI' | 'ZAID';
  }];

  cropRotationPlan: [{
    plotId: string;
    season: 'KHARIF' | 'RABI' | 'ZAID';
    year: number;
    plannedCrop: string;
    variety?: string;
    status: 'PLANNED' | 'PLANTED' | 'HARVESTED';
  }];

  farmingPractices: {
    primaryMethod: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
    irrigationSystems: string[];
    sustainabilityScore?: number;
  };

  createdAt: Date;
  updatedAt: Date;
}

/**
 * Farm schema definition
 */
const FarmSchema = new Schema(
  {
    farmId: {
      type: String,
      required: true,
      unique: true,
    },
    farmerId: {
      type: String,
      required: true,
      // Note: This references the farmerId field in Farmer model, not ObjectId
    },
    name: {
      type: String,
      required: true,
    },
    location: {
      country: {
        type: String,
        required: true,
        default: 'INDIA'
      },
      state: {
        type: String,
        required: true,
      },
      city: {
        type: String,
        required: true,
      },
      pincode: {
        type: String,
        required: true,
      },
      addressLine1: {
        type: String,
        required: true,
      },
      addressLine2: {
        type: String,
        required: true,
      },
      coordinates: {
        latitude: {
          type: Number,
        },
        longitude: {
          type: Number,
        },
      },
    },
    totalArea: {
      type: Number,
      required: true,
    },
    soilType: {
      type: String,
      required: true,
    },
    waterSource: {
      type: String,
      required: true,
    },
    infrastructure: [
      {
        type: String,
      },
    ],
    certifications: [
      {
        type: String,
      },
    ],
    status: {
      type: String,
      enum: ['ACTIVE', 'INACTIVE'],
      default: 'ACTIVE',
    },
    plots: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Plot',
      },
    ],

    // Enhanced farm management fields
    currentCrops: [
      {
        cropId: {
          type: String,
          required: true,
        },
        plotId: {
          type: String,
          required: true,
        },
        name: {
          type: String,
          required: true,
        },
        variety: {
          type: String,
        },
        plantingDate: {
          type: Date,
          required: true,
        },
        expectedHarvestDate: {
          type: Date,
          required: true,
        },
        growthStage: {
          type: String,
          required: true,
        },
        season: {
          type: String,
          enum: ['KHARIF', 'RABI', 'ZAID'],
          required: true,
        },
      },
    ],

    cropRotationPlan: [
      {
        plotId: {
          type: String,
          required: true,
        },
        season: {
          type: String,
          enum: ['KHARIF', 'RABI', 'ZAID'],
          required: true,
        },
        year: {
          type: Number,
          required: true,
        },
        plannedCrop: {
          type: String,
          required: true,
        },
        variety: {
          type: String,
        },
        status: {
          type: String,
          enum: ['PLANNED', 'PLANTED', 'HARVESTED'],
          default: 'PLANNED',
        },
      },
    ],

    farmingPractices: {
      primaryMethod: {
        type: String,
        enum: ['ORGANIC', 'CONVENTIONAL', 'INTEGRATED'],
        default: 'CONVENTIONAL',
      },
      irrigationSystems: [
        {
          type: String,
        },
      ],
      sustainabilityScore: {
        type: Number,
        min: 0,
        max: 100,
      },
    },
  },
  {
    timestamps: true,
  }
);

// Create and export the Farm model
export const FarmModel = mongoose.model<FarmDocument>('Farm', FarmSchema);
